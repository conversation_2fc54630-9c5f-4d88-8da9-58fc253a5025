=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-16 21:48:26
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-16_09-48-26-PM.txt
==================================================

[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-16_09-48-26-PM.txt
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 21:48:26
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] ==================================================
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,844 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO - Command-line mode: Processing both
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO - 🎬 Starting Radarr (Movies) monitoring...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO - 🔄 Checking for season progression opportunities...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,846 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,847 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,847 - pipeline_02 - INFO - 🔄 Initializing real-time telemetry for download monitoring...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,848 - pipeline_02 - INFO - No valid active jobs found in state file
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,848 - pipeline_02 - INFO - ✅ Restored 1 movie candidates from state
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,848 - pipeline_02 - INFO -    🎬 ID 690: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,848 - pipeline_02 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,849 - pipeline_02 - INFO - ✅ Real-time telemetry system initialized for download monitoring
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,849 - pipeline_02 - INFO -    🛡️ Intelligent fallback protection: ENABLED
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,849 - pipeline_02 - INFO - Discovering movies by scanning filesystem...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,853 - pipeline_02 - INFO - Found 1 movies across 14 stages
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,853 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,853 - pipeline_02 - INFO -      Radarr API endpoint: http://localhost:7878
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,853 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,864 - pipeline_02 - INFO - Retrieved 1 movies from Radarr
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,865 - pipeline_02 - INFO -      Active downloads in Radarr queue: 1
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,868 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,868 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO - No movies currently in download states
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO - 🛡️  LONG PATH PRE-PROCESSING: Checking for Windows path length issues...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO -      Found 1 items to check for long paths
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO -      Checking folder: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR (107 chars)
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO -           MKV file: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR.mkv (182 chars)
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,870 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR.mkv
[2025-09-16 21:48:26] [STDOUT] [+0:00:00]   DEBUG: Checking folder: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR (143 chars)
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00]     DEBUG: MKV file: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR.mkv
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR.mkv (218 chars)
[2025-09-16 21:48:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,875 - pipeline_02 - INFO -      Long path handling completed - safe to proceed with detection
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,875 - pipeline_02 - INFO - 🔍 Checking for Windows 8.3 short name corruption...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      Found completed movie: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR (21.16 GB)
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 1 completed movies ready for organization
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      Content type filter: movie
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      ENHANCED DETECTION: Scanning for both movies and TV shows...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      FILESYSTEM SCAN: Found 0 movies and 0 TV shows
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      TOTAL CONTENT: 0 items ready for processing
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - WARNING - 🛡️  DATABASE DETECTION EMPTY - Activating filesystem fallback for edge cases
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - WARNING -      Converting 1 filesystem items to processing format...
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      FALLBACK: Added 13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR as movie
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      FALLBACK COMPLETE: Now processing 1 items total
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      Organizing completed movie: 13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR () from 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,876 - pipeline_02 - INFO -      🎬 MOVIE DETECTED: Processing 13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR as movie content
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,878 - pipeline_02 - WARNING - Could not find movie directory for fallback_13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,878 - pipeline_02 - INFO - 🔍 Analyzing original download folder: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,879 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'movie', 'resolution': '1080p', 'title': '13 Going On 30 Truehd 5 1 Avc Framestor', 'year': 2004, 'original_folder': '13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR', 'confidence': 0.95}
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,879 - pipeline_02 - INFO - 🎯 Content type: movie (confidence: 0.95)
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,879 - pipeline_02 - WARNING - 🚨 Database has messy title: '13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR'
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,882 - pipeline_02 - INFO - 🧹 Fallback cleaned title: '13 Going on 30'
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,882 - pipeline_02 - INFO - 📡 Using official title '13 Going on 30' from Radarr
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,882 - pipeline_02 - INFO - 📁 Using extracted year '2004' (no Radarr year available)
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,882 - pipeline_02 - INFO - 📝 Enhanced content_info: title='13 Going on 30', year='2004', type='movie'
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,882 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-16 21:48:26] [STDERR] [+0:00:00] 2025-09-16 21:48:26,882 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,176 - pipeline_02 - INFO - Detected resolution 1920x1080 for 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR.mkv
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,177 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,177 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,185 - pipeline_02 - INFO - ✅ Radarr movie title looks clean: '13 Going on 30'
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,185 - pipeline_02 - INFO - 🎯 FINAL MOVIE METADATA: '13 Going on 30' (2004) - cleaned from Radarr
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,186 - pipeline_02 - INFO - Organizing movie '13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR' to 'workspace\2_downloaded_and_organized\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).mkv'
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,186 - pipeline_02 - INFO - Created directory: workspace\2_downloaded_and_organized\movies\1080p\13 Going on 30 (2004)
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,186 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR.mkv' to 'workspace\2_downloaded_and_organized\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).mkv'
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,186 - pipeline_02 - INFO - Successfully organized movie file. Cleaning up raw download folder.
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,187 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR (ignore_errors=True)
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,195 - pipeline_02 - INFO - ✅ Movie organized successfully with metadata: workspace\2_downloaded_and_organized\movies\1080p\13 Going on 30 (2004)\13 Going on 30 (2004).mkv
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,261 - pipeline_02 - INFO - ✅ Successfully organized: 13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR ()
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,261 - pipeline_02 - INFO - 🧹 Starting comprehensive cleanup for 13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR ()
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,261 - pipeline_02 - INFO - 🧹 Starting enhanced Radarr cleanup for: 13 Going on 30 2004 REPACK BluRay 1080p TrueHD 5 1 AVC REMUX-FraMeSToR
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,261 - pipeline_02 - INFO -    Available IDs: radarr_id=None, tmdb_id=None, year=
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,261 - pipeline_02 - INFO - 🔍 Step 1: Getting Radarr movies and queue...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,268 - pipeline_02 - INFO -    Retrieved 1 movies from Radarr
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,269 - pipeline_02 - INFO -    Retrieved 1 queue items from Radarr
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,269 - pipeline_02 - INFO - 🎯 Step 2: Finding movie using multiple matching strategies...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,269 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,269 - pipeline_02 - INFO -    Found 1 additional queue items by title matching
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,269 - pipeline_02 - INFO -    Removing 1 queue items...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,366 - pipeline_02 - INFO -    ✅ Removed queue item: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR (ID: 882300227)
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,366 - pipeline_02 - WARNING -    Could not find movie in Radarr library - queue cleanup only
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,366 - pipeline_02 - WARNING - SABnzbd URL or API key not configured (sabnzbd_url=, sabnzbd_api_key=), skipping history cleanup
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,366 - pipeline_02 - INFO - ✅ Radarr cleanup successful, SABnzbd cleanup had issues
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,366 - pipeline_02 - INFO -      ORGANIZATION SUMMARY: 1 movies organized successfully
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,372 - pipeline_02 - INFO -      Radarr API: Retrieved 1 movies for status sync
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO - Pipeline state refreshed - found 1 movies
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO - ✅ Real-time telemetry system cleaned up
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO - ===== Finished Modern Radarr Download Monitoring =====
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO -      SUCCESS: 1 movies organized and ready for MKV processing
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,376 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,377 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,377 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,379 - pipeline_02 - INFO - Found 1 content items across 14 stages
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,379 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,379 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,379 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,382 - pipeline_02 - INFO - Retrieved 0 TV series from Sonarr
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO - No active TV show downloads in Sonarr queue
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -      No completed TV show folders found in filesystem
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -      No completed TV shows found for organization
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (Movies + TV Shows)
[2025-09-16 21:48:27] [STDERR] [+0:00:00] 2025-09-16 21:48:27,384 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 🕐 Ended at: 2025-09-16 21:48:27
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] ⏱️ Total duration: 0:00:00.544089
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-16_09-48-26-PM.txt
[2025-09-16 21:48:27] [STDOUT] [+0:00:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-16 21:48:27
Duration: 0:00:00.544089
==================================================
