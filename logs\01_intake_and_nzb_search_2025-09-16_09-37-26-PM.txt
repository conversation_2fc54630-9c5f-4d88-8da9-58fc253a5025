=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-16 21:37:26
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_09-37-26-PM.txt
==================================================

[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_09-37-26-PM.txt
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 21:37:26
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,613 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,615 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,615 - interactive_pipeline_01 - WARNING - ⚠️ Failed to evaluate auto-start for Stage 02: cannot access local variable '_get' where it is not associated with a value
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,615 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,616 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,616 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,616 - interactive_pipeline_01 - INFO -    📊 Loaded 7 existing movie records
[2025-09-16 21:37:26] [STDERR] [+0:00:00] 2025-09-16 21:37:26,616 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:26] [STDOUT] [+0:00:00]   4. Quit
[2025-09-16 21:37:26] [STDOUT] [+0:00:00] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:27] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-16 21:37:27] [STDOUT] [+0:00:01] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 📁 Loaded 6 movies from C:\Users\<USER>\Videos\PlexAutomator\new_movie_requests.txt
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] ============================================================
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 🎬 Movies Available for Processing:
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] ============================================================
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]    1. 13 Going on 30 (2004)
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]    2. Don't Breathe (2016)
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]    3. Top Gun: Maverick (2022)
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]    4. There Will Be Blood (2007)
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]    5. Star Trek Into Darkness (2013)
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]    6. The Dark Knight (2008)
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 📝 Selection Options:
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]   • Single: Enter number (e.g., '3')
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]   • All: Enter 'all' or 'a'
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]   • None: Enter 'none' or 'n' to skip
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:29] [STDOUT] [+0:00:02]   • Quit: Enter 'quit' or 'q'
[2025-09-16 21:37:29] [STDOUT] [+0:00:02] 
[2025-09-16 21:37:31] [STDOUT] [+0:00:04] ✅ Selected 1 movies:
[2025-09-16 21:37:31] [STDOUT] [+0:00:04] 
[2025-09-16 21:37:31] [STDOUT] [+0:00:04]     1. 13 Going on 30 (2004)
[2025-09-16 21:37:31] [STDOUT] [+0:00:04] 
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 🎬 Processing 1 selected movies...
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] ============================================================
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 
[2025-09-16 21:37:32] [STDERR] [+0:00:05] 2025-09-16 21:37:32,528 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 📍 Progress: 1/1
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 🎬 Processing: 13 Going on 30 (2004)
[2025-09-16 21:37:32] [STDOUT] [+0:00:05] 
[2025-09-16 21:37:32] [STDERR] [+0:00:05] 2025-09-16 21:37:32,528 - interactive_pipeline_01 - INFO - Processing movie: 13 Going on 30 (2004)
[2025-09-16 21:37:32] [STDERR] [+0:00:05] 2025-09-16 21:37:32,529 - utils.metadata_apis - INFO - Using enhanced fuzzy matching for: '13 Going on 30 (2004)'
[2025-09-16 21:37:32] [STDERR] [+0:00:05] 2025-09-16 21:37:32,530 - _internal.utils.fuzzy_matching - INFO - Starting enhanced fuzzy matching for: '13 Going on 30 (2004)' (type: movie)
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,664 - utils.metadata_apis - INFO - TMDb search for '13 Going on 30' (Year: 2004) found 1 results.
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,665 - _internal.utils.fuzzy_matching - INFO - Found 1 candidates for '13 Going on 30 (2004)' via TMDb
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,777 - _internal.utils.fuzzy_matching - INFO - Fast path: Exact title match - '13 Going on 30' (2004)
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,777 - _internal.utils.fuzzy_matching - INFO - Fast-path match found for '13 Going on 30 (2004)' (0.25s)
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,777 - utils.metadata_apis - INFO - Fetching details for TMDb ID 10096
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,885 - utils.metadata_apis - INFO - Fetched details for TMDb ID 10096: 13 Going on 30
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,886 - utils.metadata_apis - INFO - Successfully matched '13 Going on 30 (2004)' to '13 Going on 30' (97.0% confidence) via fast_path in 0.25s
[2025-09-16 21:37:32] [STDOUT] [+0:00:06] ✅ Found metadata: 13 Going on 30 (2004)
[2025-09-16 21:37:32] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:32] [STDERR] [+0:00:06] 2025-09-16 21:37:32,886 - interactive_pipeline_01 - INFO - Successfully found metadata for: 13 Going on 30 (2004)
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,214 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,215 - interactive_pipeline_01 - INFO - Searching Radarr for: 13 Going on 30 2004
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,244 - interactive_pipeline_01 - INFO - Found match: 13 Going on 30 (2004)
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,246 - interactive_pipeline_01 - INFO - 🔍 Checking for duplicates: TMDB ID 10096 in 0 existing movies
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,246 - interactive_pipeline_01 - INFO - ✅ NO DUPLICATE: Movie TMDB 10096 not found in Radarr - safe to add
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,248 - interactive_pipeline_01 - INFO - 🔁 Using existing Radarr root folder: E:\
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,248 - interactive_pipeline_01 - INFO - 📋 ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,248 - interactive_pipeline_01 - INFO - 🎬 Adding movie to Radarr with 1 quality profile(s): 13 Going on 30 2004
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,248 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 4 (searchForMovie=False)...
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,343 - interactive_pipeline_01 - INFO -    ✅ Successfully added: 13 Going on 30 2004 (ID: 690, Profile: 4)
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 📥 Queued "13 Going on 30 (2004)" for download...
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,345 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T21:37:33.345438", "event": "download_queued", "job_id": "radarr_690", "title": "13 Going on 30 (2004)", "source": "radarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "radarr_id": 690, "quality": "Unknown"}
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,345 - interactive_pipeline_01 - INFO - 📋 Enhanced tracking: 13 Going on 30 (2004)
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,345 - interactive_pipeline_01 - INFO -    🆔 Radarr ID: 690
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,345 - interactive_pipeline_01 - INFO -    📊 Job ID: radarr_690
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 📊 Movie queued for download: 13 Going on 30 (2004)
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06]    🔬 Enhanced tracking: radarr_6...
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06]    🆔 Radarr ID: 690
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06]    🛡️ Fallback protection: Enabled
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,346 - interactive_pipeline_01 - INFO - Phase 1: Enhanced telemetry job started: radarr_690 for movie 690
[2025-09-16 21:37:33] [STDERR] [+0:00:06] 2025-09-16 21:37:33,346 - interactive_pipeline_01 - INFO - Movie ID 690 tracked for accurate correlation
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 🤔 Download Strategy Choice for: 13 Going on 30
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] Choose how you want to handle downloads for this movie:
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 2. ⚡ Radarr Auto-Grab - Let Radarr immediately search and grab based on quality profiles
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 3. ⏭️  Skip - Add to Radarr but don't start any downloads yet
[2025-09-16 21:37:33] [STDOUT] [+0:00:06] 
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,562 - interactive_pipeline_01 - INFO - 🎯 Quality Strategy for 2004: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 🐛 DEBUG: sanitized movie_title = '13_Going_on_30'
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 🐛 DEBUG: final out_path = 'workspace\preflight_decisions\movies\13_Going_on_30.json'
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,562 - interactive_pipeline_01 - INFO - 🧪 Running movie preflight analysis for Radarr ID 690
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,563 - interactive_pipeline_01 - INFO - 🎯 Quality constraint: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 🔍 Fetching movie releases from Radarr...
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 🎯 Quality filter: ≤2009 movie: Using 1080p only (Profile 4) - largest file preferred
[2025-09-16 21:37:34] [STDOUT] [+0:00:07] 
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,565 - preflight_analyzer.cache_observability - INFO - Initialized enhanced cache metrics with max_events=10000
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,565 - preflight_analyzer.memory_cache - INFO - Initialized memory cache with maxsize=1000, ttl=43200s
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,574 - preflight_analyzer.persistent_cache - INFO - Initialized persistent cache at workspace\preflight_cache\cache\analysis_cache.db
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,574 - preflight_analyzer.guid_reconciler - INFO - Initialized GUID reconciler with size_tolerance=0.05, title_threshold=0.8, min_confidence=0.7, groups=True, indexers=True
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,574 - preflight_analyzer.ttl_coordinator - INFO - Initialized TTL coordinator with default policies
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,574 - preflight_analyzer.multi_layer_cache - INFO - Initialized multi-layer cache at workspace\preflight_cache\cache
[2025-09-16 21:37:34] [STDERR] [+0:00:07] 2025-09-16 21:37:34,574 - preflight_analyzer.cache - INFO - Initialized decision cache at workspace\preflight_cache\cache
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 📊 Raw releases fetched: 39
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] ✅ No duplicates found, proceeding with 39 unique releases
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 🎯 Filtering releases for strategy: 1080p_only
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 🔍 Quality filter applied: 37 releases match 1080p-only strategy
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 🔬 Dynamic scanning: analyzing all 37 available candidates
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 🎬 Analyzing 22 movie candidates in parallel (max 6 concurrent)...
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08]    🔍 21:37:35 Analyzing: 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:35] [STDOUT] [+0:00:08]    🔍 21:37:35 Analyzing: 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 21:37:35] [STDOUT] [+0:00:08] 
[2025-09-16 21:37:36] [STDOUT] [+0:00:09]    🔍 21:37:36 Analyzing: 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 21:37:36] [STDOUT] [+0:00:09] 
[2025-09-16 21:37:36] [STDOUT] [+0:00:10]    🔍 21:37:36 Analyzing: 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 21:37:36] [STDOUT] [+0:00:10] 
[2025-09-16 21:37:37] [STDOUT] [+0:00:10]    🔍 21:37:37 Analyzing: 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 21:37:37] [STDOUT] [+0:00:10] 
[2025-09-16 21:37:37] [STDOUT] [+0:00:11]    🔍 21:37:37 Analyzing: 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 21:37:37] [STDOUT] [+0:00:11] 
[2025-09-16 21:37:49] [STDOUT] [+0:00:22]    ✅ 21:37:49 Result: ACCEPT (risk: 0.0960, missing: 0.5%)
[2025-09-16 21:37:49] [STDOUT] [+0:00:22] 
[2025-09-16 21:37:49] [STDOUT] [+0:00:22]    🔍 21:37:49 Analyzing: 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO)
[2025-09-16 21:37:49] [STDOUT] [+0:00:22] 
[2025-09-16 21:37:56] [STDOUT] [+0:00:29]    ✅ 21:37:56 Result: ACCEPT (risk: 0.0947, missing: 0.0%)
[2025-09-16 21:37:56] [STDOUT] [+0:00:29] 
[2025-09-16 21:37:56] [STDOUT] [+0:00:29]    🔍 21:37:56 Analyzing: 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS
[2025-09-16 21:37:56] [STDOUT] [+0:00:29] 
[2025-09-16 21:38:00] [STDOUT] [+0:00:33]    ✅ 21:38:00 Result: ACCEPT (risk: 0.0054, missing: 0.0%)
[2025-09-16 21:38:00] [STDOUT] [+0:00:33] 
[2025-09-16 21:38:00] [STDOUT] [+0:00:33]    🔍 21:38:00 Analyzing: 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 21:38:00] [STDOUT] [+0:00:33] 
[2025-09-16 21:38:01] [STDOUT] [+0:00:35]    ✅ 21:38:01 Result: ACCEPT (risk: 0.0005, missing: 0.0%)
[2025-09-16 21:38:01] [STDOUT] [+0:00:35] 
[2025-09-16 21:38:01] [STDOUT] [+0:00:35]    🔍 21:38:01 Analyzing: 13.Going.on.30.2004.720p.BluRay.x264-METiS
[2025-09-16 21:38:01] [STDOUT] [+0:00:35] 
[2025-09-16 21:38:03] [STDOUT] [+0:00:37]    ✅ 21:38:03 Result: ACCEPT (risk: 0.0211, missing: 0.0%)
[2025-09-16 21:38:03] [STDOUT] [+0:00:37] 
[2025-09-16 21:38:03] [STDOUT] [+0:00:37]    🔍 21:38:03 Analyzing: 13.Going.on.30.2004.1080p.BluRay.x264-OFT
[2025-09-16 21:38:03] [STDOUT] [+0:00:37] 
[2025-09-16 21:38:06] [STDOUT] [+0:00:39]    ✅ 21:38:06 Result: ACCEPT (risk: 0.0154, missing: 0.0%)
[2025-09-16 21:38:06] [STDOUT] [+0:00:39] 
[2025-09-16 21:38:06] [STDOUT] [+0:00:39]    🔍 21:38:06 Analyzing: 13.Going.on.30.2004.1080p.BluRay.x264-nikt0
[2025-09-16 21:38:06] [STDOUT] [+0:00:39] 
[2025-09-16 21:38:14] [STDOUT] [+0:00:47]    ✅ 21:38:14 Result: ACCEPT (risk: 0.0079, missing: 0.0%)
[2025-09-16 21:38:14] [STDOUT] [+0:00:47] 
[2025-09-16 21:38:14] [STDOUT] [+0:00:47]    🔍 21:38:14 Analyzing: 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC
[2025-09-16 21:38:14] [STDOUT] [+0:00:47] 
[2025-09-16 21:38:19] [STDOUT] [+0:00:53]    ✅ 21:38:19 Result: ACCEPT (risk: 0.0207, missing: 0.0%)
[2025-09-16 21:38:19] [STDOUT] [+0:00:53] 
[2025-09-16 21:38:19] [STDOUT] [+0:00:53]    🔍 21:38:19 Analyzing: 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC
[2025-09-16 21:38:19] [STDOUT] [+0:00:53] 
[2025-09-16 21:38:25] [STDOUT] [+0:00:58]    ✅ 21:38:25 Result: ACCEPT (risk: 0.0962, missing: 0.0%)
[2025-09-16 21:38:25] [STDOUT] [+0:00:58] 
[2025-09-16 21:38:25] [STDOUT] [+0:00:58]    🔍 21:38:25 Analyzing: 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD
[2025-09-16 21:38:25] [STDOUT] [+0:00:58] 
[2025-09-16 21:38:26] [STDOUT] [+0:00:59]    ✅ 21:38:26 Result: ACCEPT (risk: 0.1549, missing: 0.0%)
[2025-09-16 21:38:26] [STDOUT] [+0:00:59] 
[2025-09-16 21:38:26] [STDOUT] [+0:00:59]    🔍 21:38:26 Analyzing: 13.Going.on.30.2004.1080p.BluRay.x264-METiS
[2025-09-16 21:38:26] [STDOUT] [+0:00:59] 
[2025-09-16 21:38:29] [STDOUT] [+0:01:02]    ✅ 21:38:29 Result: ACCEPT (risk: 0.0033, missing: 0.0%)
[2025-09-16 21:38:29] [STDOUT] [+0:01:02] 
[2025-09-16 21:38:29] [STDOUT] [+0:01:02]    🔍 21:38:29 Analyzing: 13 Going on 30 2004.1080p.AC3-NoGroup
[2025-09-16 21:38:29] [STDOUT] [+0:01:02] 
[2025-09-16 21:38:30] [STDOUT] [+0:01:03]    ✅ 21:38:30 Result: ACCEPT (risk: 0.0095, missing: 0.3%)
[2025-09-16 21:38:30] [STDOUT] [+0:01:03] 
[2025-09-16 21:38:30] [STDOUT] [+0:01:03]    🔍 21:38:30 Analyzing: 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 21:38:30] [STDOUT] [+0:01:03] 
[2025-09-16 21:38:38] [STDOUT] [+0:01:12]    ✅ 21:38:38 Result: ACCEPT (risk: 0.0894, missing: 0.0%)
[2025-09-16 21:38:38] [STDOUT] [+0:01:12] 
[2025-09-16 21:38:38] [STDOUT] [+0:01:12]    🔍 21:38:38 Analyzing: 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz
[2025-09-16 21:38:38] [STDOUT] [+0:01:12] 
[2025-09-16 21:38:43] [STDOUT] [+0:01:16]    ✅ 21:38:43 Result: ACCEPT (risk: 0.0966, missing: 0.0%)
[2025-09-16 21:38:43] [STDOUT] [+0:01:16] 
[2025-09-16 21:38:43] [STDOUT] [+0:01:16]    🔍 21:38:43 Analyzing: 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed
[2025-09-16 21:38:43] [STDOUT] [+0:01:16] 
[2025-09-16 21:38:47] [STDOUT] [+0:01:20]    ✅ 21:38:47 Result: ACCEPT (risk: 0.0002, missing: 0.0%)
[2025-09-16 21:38:47] [STDOUT] [+0:01:20] 
[2025-09-16 21:38:47] [STDOUT] [+0:01:20]    🔍 21:38:47 Analyzing: 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON
[2025-09-16 21:38:47] [STDOUT] [+0:01:20] 
[2025-09-16 21:38:49] [STDOUT] [+0:01:23]    ✅ 21:38:49 Result: ACCEPT (risk: 0.0026, missing: 0.0%)
[2025-09-16 21:38:49] [STDOUT] [+0:01:23] 
[2025-09-16 21:38:49] [STDOUT] [+0:01:23]    🔍 21:38:49 Analyzing: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:38:49] [STDOUT] [+0:01:23] 
[2025-09-16 21:38:53] [STDOUT] [+0:01:26]    ✅ 21:38:53 Result: ACCEPT (risk: 0.0062, missing: 0.0%)
[2025-09-16 21:38:53] [STDOUT] [+0:01:26] 
[2025-09-16 21:38:54] [STDOUT] [+0:01:28]    ✅ 21:38:54 Result: ACCEPT (risk: 0.2048, missing: 0.0%)
[2025-09-16 21:38:54] [STDOUT] [+0:01:28] 
[2025-09-16 21:39:01] [STDOUT] [+0:01:34]    ✅ 21:39:01 Result: ACCEPT (risk: 0.0004, missing: 0.0%)
[2025-09-16 21:39:01] [STDOUT] [+0:01:34] 
[2025-09-16 21:39:06] [STDOUT] [+0:01:39]    ✅ 21:39:06 Result: ACCEPT (risk: 0.0821, missing: 0.0%)
[2025-09-16 21:39:06] [STDOUT] [+0:01:39] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:44]    ✅ 21:39:11 Result: ACCEPT (risk: 0.0336, missing: 0.0%)
[2025-09-16 21:39:11] [STDOUT] [+0:01:44] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    ✅ 21:39:11 Result: ACCEPT (risk: 0.0006, missing: 0.0%)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 📊 Analysis complete: 22 valid, 0 errors
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 🏆 Best candidate: 22.93 GB | ACCEPT | risk: 0.0006 | missing: 0.0%
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDERR] [+0:01:45] 2025-09-16 21:39:11,764 - interactive_pipeline_01 - INFO - 📝 Movie preflight decision saved: workspace\preflight_decisions\movies\13_Going_on_30.json
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 📊 Preflight Results: 22 analyzed, 22 acceptable, 0 errors
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 📊 Combined Results: 1 total movie analyzed, 22 acceptable releases found
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 🔬 Movie Preflight Analysis Results:
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #1. 🎬 13.Going.On.30.2004.720p.BluRay.X264-x0r[EXTRA-Deleted Scenes]
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 0.28 GB (300,389,461 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0960 | Missing: 0.5% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #2. 🎬 13.Going.On.30.2004.1080p.BluRay.x265
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 1.73 GB (1,860,169,468 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0154 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #3. 🎬 13.Going.On.30.2004.BDRip.1080p.x265-FLC.22
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 2.27 GB (2,436,351,791 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0211 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #4. 🎬 13.Going.On.30.2004.BDRip.1080p.X265-FLC
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 2.27 GB (2,436,930,115 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0005 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #5. 🎬 13.Going.on.30.2004.1080p.WebRip.EAC3.5.1.x265-Lootera
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 2.34 GB (2,517,640,816 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0054 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #6. 🎬 13.Going.On.30.2004.720p.BluRay.x264-x0r
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 2.63 GB (2,828,226,322 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0947 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #7. 🎬 13.Going.on.30.(2004).(1080p.BluRay.x265.10bit.HEVC.AC3.5.1.-.H4XO)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 4.56 GB (4,897,553,218 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0079 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #8. 🎬 30.ueber.Nacht.2004.German.720p.BluRay.x264-DETAiLS
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 4.59 GB (4,923,901,037 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0207 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #9. 🎬 13.Going.on.30.2004.1080p.HULU.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 4.64 GB (4,984,523,381 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0095 | Missing: 0.3% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #10. 🎬 13.Going.on.30.2004.720p.BluRay.x264-METiS
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 4.78 GB (5,136,435,650 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.1549 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #11. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-OFT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 5.12 GB (5,493,911,649 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0033 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #12. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-nikt0
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 5.12 GB (5,494,056,722 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0962 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #13. 🎬 13 Going on 30 2004 1080p NF WEB-DL DUAL DD5.1 H.264-BdC
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 5.46 GB (5,859,882,678 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0894 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #14. 🎬 13.Going.On.30.2004.720p.BluRay.DD5.1.x264-CRiSC
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 8.10 GB (8,699,365,146 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0002 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #15. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-CtrlHD
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 8.61 GB (9,247,582,818 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0966 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #16. 🎬 13.Going.on.30.2004.1080p.BluRay.x264-METiS
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 9.02 GB (9,688,109,054 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0026 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #17. 🎬 13 Going on 30 2004.1080p.AC3-NoGroup
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 9.09 GB (9,762,208,782 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.2048 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #18. 🎬 13.Going.on.30.2004.1080p.AMZN.WEB-DL.DDP.5.1.H.264-PiRaTeS
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 10.50 GB (11,273,989,818 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0062 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #19. 🎬 13.Going.On.30.2004.1080p.BluRay.x264-MonteDiaz
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 10.77 GB (11,566,818,530 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0004 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #20. 🎬 13.Going.on.30.2004.1080p.Blu-ray.Remux.AVC.Dolby.TrueHD.5.1-unc0mpressed
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 21.12 GB (22,675,404,920 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0336 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #21. 🎬 13.Going.on.30.2004.1080p.BluRay.REMUX.AVC.TrueHD.5.1-EPSiLON
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 22.84 GB (24,525,709,753 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0821 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    #22. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]        ⚡ Risk: 0.0006 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 🔬 Preflight selection (best candidate):
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    • 22.93 GB  |  ACCEPT  |  risk: 0.0006  |  missing: 0.0%
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    • Runtime: 98 min
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:11] [STDOUT] [+0:01:45]    • Release: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:11] [STDOUT] [+0:01:45] 
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 🔍 DEBUG: Checking candidate storage conditions...
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDOUT] [+0:02:25]    telemetry_integrator: True
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDOUT] [+0:02:25]    telemetry_integrator.telemetry: True
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDOUT] [+0:02:25]    best: True
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDOUT] [+0:02:25]    all_candidates: True
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 💾 Storing candidate information for fallback system...
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDERR] [+0:02:25] 2025-09-16 21:39:52,393 - interactive_pipeline_01 - INFO - 📄 Stored candidate info for Radarr ID 690
[2025-09-16 21:39:52] [STDERR] [+0:02:25] 2025-09-16 21:39:52,393 - interactive_pipeline_01 - INFO -    🎯 Candidate: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:52] [STDERR] [+0:02:25] 2025-09-16 21:39:52,394 - interactive_pipeline_01 - INFO -    👤 User selection index: 21
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] ✅ Stored candidate #22 of 22 acceptable candidates for fallback
[2025-09-16 21:39:52] [STDOUT] [+0:02:25] 
[2025-09-16 21:39:52] [STDERR] [+0:02:25] 2025-09-16 21:39:52,415 - interactive_pipeline_01 - INFO - 🎬 Movie: Mapped indexer 'NZBFinder (Prowlarr)' → ID: 2
[2025-09-16 21:39:52] [STDERR] [+0:02:25] 2025-09-16 21:39:52,415 - interactive_pipeline_01 - INFO - 🎬 Movie: Download payload: {'guid': 'https://nzbfinder.ws/details/7286961e-8aea-49c2-aa4c-28becf64f811', 'indexerId': 2}
[2025-09-16 21:39:52] [STDERR] [+0:02:25] 2025-09-16 21:39:52,415 - interactive_pipeline_01 - INFO - 🎬 Movie: Checking if '13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR' exists in cache...
[2025-09-16 21:39:52] [STDERR] [+0:02:26] 2025-09-16 21:39:52,886 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Found release in cache, proceeding with download
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,652 - interactive_pipeline_01 - INFO - 🎬 Movie: ✅ Direct download successful: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] ✅ Movie download started: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]    📥 Size: 22.93 GB | Risk: 0.0006 | Decision: ACCEPT
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,653 - interactive_pipeline_01 - INFO - 📝 Updated NZB filename for radarr_690: '13 Going on 30 (2004)' → '13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR'
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,653 - interactive_pipeline_01 - INFO - 📝 Updated telemetry with NZB filename: 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] ✅ Preflight found and started download for 1 movie
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 🔬 Preflight Movie Selection (downloading):
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]    #1. 🎬 13.Going.on.30.2004.REPACK.BluRay.1080p.TrueHD.5.1.AVC.REMUX-FraMeSToR
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]        💾 Size: 22.93 GB (24,616,529,445 bytes)
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]        ⚡ Risk: 0.0006 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]        ⏱️ Runtime: 98 min
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 📊 Movie Preflight Summary: 1 movie | Total: 22.93 GB
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 🎯 Download started immediately after analysis
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,656 - interactive_pipeline_01 - INFO - Stored metadata for: 13 Going on 30 (2004)
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] ============================================================
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 📊 Processing Complete!
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] ============================================================
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 🎬 Movies processed: 1
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 📺 TV shows processed: 0
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 📊 Total content processed: 1
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 🔍 Verifying 1 downloads actually started...
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]    This replaces guesswork with real verification!
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,657 - interactive_pipeline_01 - INFO - 🔄 Starting real-time download monitoring (interval: 5s)
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,659 - interactive_pipeline_01 - INFO - 📦 SABnzbd match found: '13 Going on 30 (2004)' -> '13.going.on.30.2004.repack.bluray.1080p.truehd.5.1.avc.remux-framestor' (similarity: 1.00)
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] ✅ Download verified: "13 Going on 30 (2004)" is now downloading
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,659 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T21:39:55.659308", "event": "download_verified", "job_id": "radarr_690", "title": "13 Going on 30 (2004)", "source": "radarr", "status": "downloading", "progress": 0.0, "size_total": ***********, "size_downloaded": 0, "speed_bps": 0.0, "eta": "0:00:00", "radarr_id": 690, "sab_nzo_id": "SABnzbd_nzo_t7gmxqw6", "quality": "Unknown"}
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,783 - interactive_pipeline_01 - INFO - 🔧 Intelligent Fallback System initialized
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,783 - interactive_pipeline_01 - INFO -    📁 Preflight decisions: workspace\preflight_decisions
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,783 - interactive_pipeline_01 - INFO -    🎬 Radarr URL: http://localhost:7878
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,783 - interactive_pipeline_01 - INFO -    🔑 API Key configured: ✅
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,783 - interactive_pipeline_01 - INFO -    📊 Telemetry integration: ✅
[2025-09-16 21:39:55] [STDERR] [+0:02:29] 2025-09-16 21:39:55,783 - interactive_pipeline_01 - INFO - 🛡️ Intelligent fallback system initialized with telemetry integration
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 📊 Real-time telemetry monitoring started
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 📄 Telemetry dashboard log: logs\telemetry_dashboard_2025-09-16_09-39-55-PM.txt
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:39:55] [STDOUT] [+0:02:29]    (Dashboard output will be written to separate file to keep main log clean)
[2025-09-16 21:39:55] [STDOUT] [+0:02:29] 
[2025-09-16 21:48:26] [STDERR] [+0:10:59] 2025-09-16 21:48:26,472 - interactive_pipeline_01 - INFO - 🔄 Triggering post-processing for completed download: 13 Going on 30 (2004)
[2025-09-16 21:48:26] [STDERR] [+0:10:59] 2025-09-16 21:48:26,476 - interactive_pipeline_01 - INFO - ✅ Post-processing triggered for: 13 Going on 30 (2004) (PID: 76244)
