#!/usr/bin/env python3
"""
Cache Maintenance and Management Script.

This script provides utilities for maintaining the enhanced multi-layer cache:
- Cleanup expired entries
- Generate performance reports
- Migrate legacy data
- Health monitoring
- Cache statistics
"""

import argparse
import logging
import sys
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def cleanup_cache(cache_path: Path, dry_run: bool = False):
    """Clean up expired cache entries."""
    print(f"🧹 Cleaning up cache at {cache_path}")
    
    from preflight_analyzer.history_store import DecisionHistory
    
    cache = DecisionHistory(cache_path)
    
    if dry_run:
        print("DRY RUN - No changes will be made")
        stats = cache.get_stats()
        print(f"Current cache entries: {stats['l2_persistent'].get('total_entries', 0)}")
    else:
        deleted_count = cache.cleanup_expired()
        print(f"Deleted {deleted_count} expired entries")
    
    cache.close()


def generate_report(cache_path: Path, output_path: Path = None):
    """Generate comprehensive cache performance report."""
    print(f"📊 Generating performance report for cache at {cache_path}")
    
    from preflight_analyzer.history_store import DecisionHistory
    
    cache = DecisionHistory(cache_path)
    
    # Get performance report
    report = cache.get_performance_report()
    
    if output_path:
        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w') as f:
            f.write(report)
        print(f"Report saved to {output_path}")
    else:
        print("\n" + report)
    
    cache.close()


def migrate_legacy_data(legacy_path: Path, new_cache_path: Path):
    """Migrate data from legacy cache to enhanced cache."""
    print(f"🔄 Migrating legacy data from {legacy_path} to {new_cache_path}")
    
    from preflight_analyzer.cache_migration import CacheMigrator
    from preflight_analyzer.multi_layer_cache import MultiLayerCache
    
    # Initialize new cache
    cache_dir = new_cache_path.parent / "cache"
    multi_cache = MultiLayerCache(cache_dir)
    
    # Run migration
    migrator = CacheMigrator(multi_cache)
    
    if legacy_path.name == "decision_history.json":
        stats = migrator.migrate_decision_history(legacy_path)
    else:
        stats = migrator.migrate_decision_files(legacy_path)
    
    print(f"Migration completed: {stats}")
    multi_cache.close()


def show_cache_stats(cache_path: Path):
    """Show detailed cache statistics."""
    print(f"📈 Cache statistics for {cache_path}")
    
    from preflight_analyzer.history_store import DecisionHistory
    
    cache = DecisionHistory(cache_path)
    stats = cache.get_stats()
    
    print("\n=== Cache Statistics ===")
    
    # Request statistics
    req_stats = stats['requests']
    print(f"Total Requests: {req_stats['total']:,}")
    print(f"Cache Hits: {req_stats['total_hits']:,}")
    print(f"Cache Misses: {req_stats['misses']:,}")
    print(f"Hit Rate: {req_stats['hit_rate']:.1%}")
    print(f"GUID Reconciliations: {req_stats['guid_reconciliations']:,}")
    
    # Layer breakdown
    print(f"\nL1 Memory Cache:")
    l1_stats = stats['l1_memory']
    print(f"  Hits: {l1_stats['hits']:,}")
    print(f"  Size: {l1_stats['guid_cache_size']}/{l1_stats['maxsize']}")
    print(f"  Hit Rate: {l1_stats['hit_rate']:.1%}")
    
    print(f"\nL2 Persistent Cache:")
    l2_stats = stats['l2_persistent']
    print(f"  Total Entries: {l2_stats.get('total_entries', 0):,}")
    print(f"  GUID Mappings: {l2_stats.get('guid_mappings', 0):,}")
    print(f"  Recent Activity: {l2_stats.get('recent_access_count', 0):,} (last 7 days)")
    
    # Content type breakdown
    if 'by_content_type' in l2_stats:
        print(f"\nContent Type Breakdown:")
        for content_type, count in l2_stats['by_content_type'].items():
            print(f"  {content_type.title()}: {count:,}")
    
    cache.close()


def health_check(cache_path: Path):
    """Perform cache health check."""
    print(f"🏥 Health check for cache at {cache_path}")
    
    from preflight_analyzer.history_store import DecisionHistory
    
    cache = DecisionHistory(cache_path)
    health = cache.get_health_status()
    
    print(f"\nOverall Status: {health['overall_status'].upper()}")
    
    # Show check results
    for check_name, check_result in health['checks'].items():
        status_icon = "✅" if check_result['status'] == 'ok' else "⚠️" if check_result['status'] == 'warning' else "❌"
        print(f"{status_icon} {check_name.replace('_', ' ').title()}: {check_result.get('value', 'N/A')}")
    
    # Show alerts
    if health['alerts']:
        print("\nAlerts:")
        for alert in health['alerts']:
            print(f"⚠️  {alert}")
    else:
        print("\n✅ No alerts")
    
    cache.close()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Cache Maintenance and Management")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up expired cache entries')
    cleanup_parser.add_argument('cache_path', type=Path, help='Path to cache file')
    cleanup_parser.add_argument('--dry-run', action='store_true', help='Show what would be deleted without actually deleting')
    
    # Report command
    report_parser = subparsers.add_parser('report', help='Generate performance report')
    report_parser.add_argument('cache_path', type=Path, help='Path to cache file')
    report_parser.add_argument('--output', type=Path, help='Output file for report')
    
    # Migrate command
    migrate_parser = subparsers.add_parser('migrate', help='Migrate legacy cache data')
    migrate_parser.add_argument('legacy_path', type=Path, help='Path to legacy cache file or directory')
    migrate_parser.add_argument('new_cache_path', type=Path, help='Path for new cache')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show cache statistics')
    stats_parser.add_argument('cache_path', type=Path, help='Path to cache file')
    
    # Health command
    health_parser = subparsers.add_parser('health', help='Perform health check')
    health_parser.add_argument('cache_path', type=Path, help='Path to cache file')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'cleanup':
            cleanup_cache(args.cache_path, args.dry_run)
        elif args.command == 'report':
            generate_report(args.cache_path, args.output)
        elif args.command == 'migrate':
            migrate_legacy_data(args.legacy_path, args.new_cache_path)
        elif args.command == 'stats':
            show_cache_stats(args.cache_path)
        elif args.command == 'health':
            health_check(args.cache_path)
        
        print("\n✅ Operation completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Operation failed: {e}")
        logger.exception("Detailed error information:")
        sys.exit(1)


if __name__ == "__main__":
    main()
