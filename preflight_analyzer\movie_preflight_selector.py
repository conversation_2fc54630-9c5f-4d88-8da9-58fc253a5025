from __future__ import annotations
"""Programmatic movie preflight selection (Radarr) akin to TV integrated selector.

Responsibilities:
- Given a Radarr movie_id, fetch manual search releases (optional manual search trigger).
- Fetch up to max_candidates releases.
- Download NZB for each via indexer API (uid supported), parse + analyze structure.
- Rank using orchestrator_common.choose_best.
- Return structured dict: { 'movie_id', 'candidates': [...], 'best': {...}|None, 'stats': {...} }

No grabbing performed here; caller executes grab to keep orchestration explicit.
"""
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List

from .orchestrator_common import load_config, choose_best
from .history_store import DecisionHistory
from .cache import DecisionCache
from .indexer_client import extract_newznab_id, fetch_nzb
from .nzb_parser import NZBParser
from .core_analyzer import analyze_metadata
from .radarr_client import manual_search_movie, fetch_releases_for_movie
from .shared_logic import BasicRelease, rank_releases

async def _analyze_release(meta: Any, servers: Any, sample_cap: int, retention_days: int = 4000):
    """Same analysis function as TV shows use"""
    return await analyze_metadata(meta, servers=servers, retention_days=retention_days, dry_run=False, verbose=False, sample_cap=sample_cap)

async def _analyze_movie_release_parallel(rel, idx_cfg, parser, servers, sample_cap, semaphore, movie_id, decision_cache=None, analysis_mode="standard"):
    """Analyze a single movie release with concurrency control and caching (same as TV shows)"""
    async with semaphore:  # Limit concurrent analyses
        try:
            nzb_id = extract_newznab_id(rel.guid)
            if not nzb_id:
                return {'guid': rel.guid, 'indexer': rel.indexer, 'error': 'No NZB ID', 'title': rel.title, 'movie_id': movie_id}

            # Create cache key based on NZB content (same as TV)
            cache_key = f"{rel.indexer}:{nzb_id}"

            # Check cache first with analysis mode
            if decision_cache:
                cached_result = decision_cache.get(cache_key, analysis_mode)
                if cached_result:
                    missing_ratio = cached_result.get('missing_ratio', 0.0)
                    print(f"   💾 {datetime.now().strftime('%H:%M:%S')} Cache hit: {rel.title} → {cached_result['decision']} (risk: {cached_result.get('risk', 0.0):.4f}, missing: {missing_ratio:.1%})")
                    return {
                        'guid': rel.guid,
                        'indexer': rel.indexer,
                        'title': rel.title,
                        'movie_id': movie_id,
                        'decision': cached_result['decision'],
                        'risk_score': cached_result.get('risk', 0.0),
                        'probe_missing_ratio': cached_result.get('missing_ratio', 0.0),
                        'size': rel.size,
                        'cached': True
                    }

            print(f"   🔍 {datetime.now().strftime('%H:%M:%S')} Analyzing: {rel.title}")
            raw_bytes = fetch_nzb(idx_cfg["base_url"], idx_cfg["api_key"], nzb_id, uid=idx_cfg.get("uid"))
            meta = parser.parse_bytes(raw_bytes)
            report = await _analyze_release(meta, servers, sample_cap=sample_cap)
            decision = report.get('decision', 'UNKNOWN')
            risk_score = report.get('risk_score', 0.0)
            missing_ratio = report.get('probe_missing_ratio', 0.0)
            print(f"   ✅ {datetime.now().strftime('%H:%M:%S')} Result: {decision} (risk: {risk_score:.4f}, missing: {missing_ratio:.1%})")

            # Store in cache with analysis mode
            if decision_cache:
                decision_cache.put(cache_key, decision, report, analysis_mode)

            report.update({
                "guid": rel.guid,
                "indexer": rel.indexer,
                "title": rel.title,
                "size": rel.size,
                "movie_id": movie_id,
            })
            return report
        except Exception as e:
            print(f"   ❌ {datetime.now().strftime('%H:%M:%S')} Error: {str(e)}")
            return {'guid': rel.guid, 'indexer': rel.indexer, 'error': str(e), 'title': rel.title, 'movie_id': movie_id}


def _load_servers(cfg_path: Path | None):
    if not cfg_path:
        return None
    from .analyze_release import load_servers
    return load_servers(cfg_path)


async def preflight_movie(
    config_path: Path | str,
    servers_config_path: Path | str | None,
    radarr_movie_id: int,
    radarr_url: str,
    radarr_api_key: str,
    manual_search: bool = True,
    max_candidates: int = 15,
    sample_cap: int = 500,
    quality_strategy: Dict[str, Any] = None,
    settings_dict: Dict[str, Any] = None,  # New parameter for settings
    analysis_mode: str = "reliability",  # Analysis mode: 'standard', 'reliability', 'hybrid'
) -> Dict[str, Any]:
    """
    Perform preflight analysis for a Radarr movie with configurable analysis modes.
    
    Args:
        config_path: Path to preflight configuration file
        servers_config_path: Path to NNTP servers configuration
        radarr_movie_id: Radarr movie ID
        radarr_url: Radarr base URL
        radarr_api_key: Radarr API key
        manual_search: Whether to trigger manual search
        max_candidates: Maximum number of releases to analyze
        sample_cap: Maximum segments to sample per release
        quality_strategy: Quality selection strategy
        settings_dict: Additional settings configuration
        analysis_mode: Analysis mode controlling cache behavior:
            - 'standard': Normal mode with full deduplication (default)
            - 'reliability': Research mode, bypasses cache for individual analysis
            - 'hybrid': Partial deduplication, reuses metadata but performs fresh reliability checks
    
    Returns:
        Dictionary containing analysis results, candidates, and statistics
    """
    cfg_path = Path(config_path) if not isinstance(config_path, Path) else config_path
    cfg = load_config(cfg_path)
    indexers_cfg = {i['name']: i for i in cfg.get('indexers', [])}

    # Dynamic scanning configuration from settings
    dynamic_scanning_enabled = False
    dynamic_max_threshold = 100
    large_pool_limit = 50
    min_candidates = 10
    
    if settings_dict:
        search_settings = settings_dict.get('SEARCH', {})
        dynamic_scanning_enabled = search_settings.get('enable_dynamic_scanning', 'false').lower() == 'true'
        dynamic_max_threshold = int(search_settings.get('max_dynamic_scan_threshold', '100'))
        large_pool_limit = int(search_settings.get('large_pool_candidate_limit', '50'))
        min_candidates = int(search_settings.get('min_candidates_to_analyze', '10'))
    large_pool_limit = 50
    min_candidates = 10
    show_detailed = False
    
    if settings_dict:
        search_section = settings_dict.get('SEARCH', {})
        dynamic_scanning_enabled = search_section.get('enable_dynamic_scanning', 'false').lower() == 'true'
        dynamic_max_threshold = int(search_section.get('max_dynamic_scan_threshold', '100'))
        large_pool_limit = int(search_section.get('large_pool_candidate_limit', '50'))
        min_candidates = int(search_section.get('min_candidates_to_analyze', '10'))
        show_detailed = search_section.get('show_detailed_analysis', 'false').lower() == 'true'

    # Selection policy for movies: risk-first, then size tie-break within a dynamic tolerance
    selection_cfg = cfg.get('selection', {}) if isinstance(cfg, dict) else {}
    global_risk_tolerance = selection_cfg.get('risk_tolerance', None)
    try:
        risk_tolerance = float(global_risk_tolerance) if global_risk_tolerance is not None else None
    except Exception:
        risk_tolerance = None
    prefer_larger_size = bool(selection_cfg.get('prefer_larger_size', True))

    def _pick_best_candidate(cands: List[Dict[str, Any]]) -> Dict[str, Any] | None:
        if not cands:
            return None
        def _risk(x: Dict[str, Any]) -> float:
            try:
                return float(x.get('risk_score'))
            except Exception:
                return float('inf')
        def _size(x: Dict[str, Any]) -> int:
            try:
                return int(x.get('size', 0) or 0)
            except Exception:
                return 0
        risks = [_risk(x) for x in cands]
        best_risk = min(risks, default=float('inf'))

        # Determine tolerance: global override or dynamic per-episode (IQR-based)
        if isinstance(risk_tolerance, float):
            tol = risk_tolerance
        else:
            try:
                import statistics as _stats
                if len(risks) >= 4:
                    qs = _stats.quantiles(risks, n=4, method='inclusive')
                    q1, q3 = qs[0], qs[2]
                    iqr = max(0.0, q3 - q1)
                else:
                    rmin, rmax = min(risks), max(risks)
                    iqr = max(0.0, (rmax - rmin) * 0.5)
                dyn = 0.01 + 0.6 * iqr
                tol = min(0.08, max(0.01, dyn))
            except Exception:
                tol = 0.03

        near_best = [x for x in cands if (_risk(x) - best_risk) <= tol + 1e-9]
        if prefer_larger_size and near_best:
            return max(near_best, key=_size)
        return min(cands, key=_risk)

    # Load servers exactly like TV shows
    servers = None
    if servers_config_path:
        from .analyze_release import load_servers
        sc_path = Path(servers_config_path) if not isinstance(servers_config_path, Path) else servers_config_path
        servers = load_servers(sc_path)

    # Initialize caching system with enhanced multi-layer cache
    cache_dir = Path('workspace') / 'preflight_cache'
    cache_dir.mkdir(parents=True, exist_ok=True)
    cache_file = cache_dir / 'decision_history.json'
    decision_cache = DecisionCache(cache_file, ttl_seconds=12 * 3600)  # Use new multi-layer cache

    if manual_search:
        try:
            manual_search_movie(radarr_url, radarr_api_key, radarr_movie_id)
        except Exception:
            pass

    raw_releases = fetch_releases_for_movie(radarr_url, radarr_api_key, radarr_movie_id)

    # CRITICAL FIX #1: Deduplicate releases to prevent redundant analysis
    print(f"📊 Raw releases fetched: {len(raw_releases)}")
    seen_guids = set()
    unique_releases = []
    duplicates_removed = 0
    
    for release in raw_releases:
        guid = release.get('guid', '')
        if guid and guid not in seen_guids:
            seen_guids.add(guid)
            unique_releases.append(release)
        elif guid:
            duplicates_removed += 1
    
    raw_releases = unique_releases
    if duplicates_removed > 0:
        print(f"🧹 Removed {duplicates_removed} duplicate releases, {len(raw_releases)} unique releases remain")
    else:
        print(f"✅ No duplicates found, proceeding with {len(raw_releases)} unique releases")

    # Filter releases by quality strategy (CRITICAL FIX)
    if quality_strategy:
        strategy_type = quality_strategy.get('strategy', '')
        print(f"🎯 Filtering releases for strategy: {strategy_type}")

        if strategy_type == '1080p_only':
            # Only keep 1080p releases, reject 4K/2160p - use consistent quality detection
            filtered_releases = []
            for release in raw_releases:
                title = (release.get('title') or '').upper()  # Use uppercase for consistency
                # Reject 4K/2160p releases
                if any(x in title for x in ['4K', '2160P', 'UHD']):
                    continue
                # Accept 1080p and other HD releases
                if any(x in title for x in ['1080P', '720P', 'BLURAY', 'WEB-DL', 'WEBRIP']):
                    filtered_releases.append(release)
            raw_releases = filtered_releases
            print(f"🔍 Quality filter applied: {len(raw_releases)} releases match 1080p-only strategy")
        elif strategy_type == '4k_only':
            # Only keep 4K/2160p releases - use consistent quality detection
            filtered_releases = []
            for release in raw_releases:
                title = (release.get('title') or '').upper()  # Use uppercase for consistency
                # Match the same logic as determine_quality_from_title 
                if any(x in title for x in ['4K', '2160P', 'UHD']):
                    filtered_releases.append(release)
            raw_releases = filtered_releases
            print(f"🔍 Quality filter applied: {len(raw_releases)} releases match 4K-only strategy")
        # For 'both' strategy, keep all releases

    # Quality fallback logic - if no releases found, try fallback strategy
    if not raw_releases and quality_strategy:
        strategy_type = quality_strategy.get('strategy', '')
        if strategy_type == '4k_only':
            print("🔄 No 4K releases found, attempting fallback to 1080p...")
            # Re-fetch all releases and apply 1080p filter
            raw_releases = fetch_releases_for_movie(radarr_url, radarr_api_key, radarr_movie_id)
            filtered_releases = []
            for release in raw_releases:
                title = (release.get('title') or '').upper()  # Use uppercase for consistency
                # Reject 4K/2160p releases, accept 1080p and other HD
                if any(x in title for x in ['4K', '2160P', 'UHD']):
                    continue
                if any(x in title for x in ['1080P', '720P', 'BLURAY', 'WEB-DL', 'WEBRIP']):
                    filtered_releases.append(release)
            raw_releases = filtered_releases
            if raw_releases:
                print(f"✅ Fallback successful: {len(raw_releases)} 1080p releases found")
            else:
                print("❌ Fallback failed: No 1080p releases available")

    if not raw_releases:
        return {"movie_id": radarr_movie_id, "candidates": [], "best": None, "error": "No releases match quality strategy (including fallback)"}

    # Dynamic candidate limit calculation
    total_available = len(raw_releases)
    effective_max_candidates = max_candidates  # Default fallback
    
    if dynamic_scanning_enabled:
        if total_available <= dynamic_max_threshold:
            # Analyze all available candidates when pool is manageable
            effective_max_candidates = total_available
            print(f"🔬 Dynamic scanning: analyzing all {total_available} available candidates")
        elif total_available > dynamic_max_threshold:
            # Use large pool limit for very large candidate pools
            effective_max_candidates = large_pool_limit
            print(f"🔬 Dynamic scanning: analyzing top {large_pool_limit} of {total_available} candidates (large pool)")
        else:
            # Ensure minimum candidate analysis
            effective_max_candidates = max(min_candidates, max_candidates)
            print(f"🔬 Dynamic scanning: analyzing {effective_max_candidates} candidates")
    else:
        print(f"🔬 Fixed scanning: analyzing {max_candidates} candidates (max limit)")

    parser = NZBParser()
    candidate_reports: List[Dict[str, Any]] = []
    basic: List[BasicRelease] = []
    seen_titles = set()  # Deduplication by title

    for rel in raw_releases:
        if len(basic) >= effective_max_candidates:
            break
        guid = rel.get("guid", "")
        title = str(rel.get("title") or guid)

        # Deduplicate by title (same as TV shows)
        if title in seen_titles:
            continue
        seen_titles.add(title)

        idx_name = rel.get("indexer") or rel.get("indexerName") or ""
        idx_cfg = indexers_cfg.get(idx_name)
        if not idx_cfg:
            continue
        nzb_id = extract_newznab_id(guid)
        if not nzb_id:
            continue
        size = int(rel.get("size") or 0)
        basic.append(
            BasicRelease(
                guid=guid,
                title=title,
                size=size,
                indexer=idx_name,
                raw=rel,  # keep original
            )
        )

    ranked = rank_releases(basic, limit=effective_max_candidates)
    print(f"🎬 Analyzing {len(ranked)} movie candidates in parallel (max 6 concurrent)...")

    # Create semaphore to limit concurrent analyses (same as TV)
    semaphore = asyncio.Semaphore(6)

    # Prepare tasks for parallel analysis with caching (same pattern as TV)
    tasks = []
    for rel in ranked:
        idx_cfg = indexers_cfg.get(rel.indexer or "")
        if not idx_cfg:
            continue
        nzb_id = extract_newznab_id(rel.guid)
        if not nzb_id:
            continue
        task = _analyze_movie_release_parallel(rel, idx_cfg, parser, servers, sample_cap, semaphore, radarr_movie_id, decision_cache, analysis_mode)
        tasks.append(task)

    # Execute all analyses in parallel (same as TV shows)
    if tasks:
        candidate_reports = await asyncio.gather(*tasks, return_exceptions=True)
        # Filter out exceptions and convert to proper format (same as TV)
        candidate_reports = [r for r in candidate_reports if isinstance(r, dict)]
    else:
        candidate_reports = []

    # Filter valid reports and choose best (same logic as TV)
    valid_reports = [r for r in candidate_reports if 'decision' in r and 'error' not in r]
    error_reports = [r for r in candidate_reports if 'error' in r]

    # Calculate cache statistics (same as TV)
    cache_hits = sum(1 for report in candidate_reports if report.get('cached', False))
    total_analyses = len(candidate_reports)
    cache_hit_rate = (cache_hits / total_analyses) if total_analyses > 0 else 0.0

    if cache_hits > 0:
        print(f"💾 Cache performance: {cache_hits}/{total_analyses} hits ({cache_hit_rate:.1%}) - saved significant analysis time!")

    print(f"📊 Analysis complete: {len(valid_reports)} valid, {len(error_reports)} errors")

    # Choose best: prefer accepted items; dynamic risk window + size tiebreak
    acceptable_reports = [r for r in valid_reports if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]
    if acceptable_reports:
        best = _pick_best_candidate(acceptable_reports) or choose_best(acceptable_reports)
    elif valid_reports:
        best = _pick_best_candidate(valid_reports) or choose_best(valid_reports)
    else:
        best = None

    # Enhanced stats (mirrors TV implementation)
    acceptable_reports = [r for r in valid_reports if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]
    stats = {
        'total_candidates': len(candidate_reports),
        'valid_analyses': len(valid_reports),
        'acceptable': len(acceptable_reports),
        'errors': len(error_reports),
        'cache_hits': cache_hits,
        'cache_hit_rate': cache_hit_rate,
        'best_decision': best.get('decision') if best else None,
        'best_risk_score': best.get('risk_score') if best else None,
        'best_missing_ratio': best.get('probe_missing_ratio') if best else None,
        'best_size_gb': round((best.get('size', 0) / (1024**3)), 2) if best else None
    }

    # Show summary like TV does
    if best:
        decision = best.get('decision', 'UNKNOWN')
        risk_score = best.get('risk_score', 0.0)
        size_gb = stats['best_size_gb']
        missing_ratio = best.get('probe_missing_ratio', 0.0)
        print(f"🏆 Best candidate: {size_gb} GB | {decision} | risk: {risk_score:.4f} | missing: {missing_ratio:.1%}")
    else:
        print("⚠️ No acceptable candidates found")

    return {
        'movie_id': radarr_movie_id,
        'candidates': candidate_reports,
        'valid_candidates': valid_reports,
        'acceptable_candidates': acceptable_reports,
        'best': best,
        'strategy': 'movie_single',
        'stats': stats,
        'success': best is not None and best.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')
    }

__all__ = ['preflight_movie']
