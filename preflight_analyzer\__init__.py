"""
Preflight Analyzer with Multi-Layer Cache Architecture and Unified TV Show Analysis.

This module provides the preflight analyzer with a robust
multi-layer cache system that dramatically reduces cache miss rates
through content-based keys and GUID reconciliation, plus a unified
TV show preflight analysis system.
"""

from .history_store import DecisionHistory
from .cache import DecisionCache
from .multi_layer_cache import MultiLayerCache
from .cache_models import AnalysisResult, Content<PERSON>ey, AnalysisMode
from .cache_observability import CacheMetrics, <PERSON>acheLogger, CacheHealthMonitor
from .cache_warming import CacheWarmer
from .ttl_coordinator import TTLCoordinator, TTLPolicy

# Unified TV Show Preflight Analysis System
from .tv_show_preflight_selector import preflight_tv_show, TvPreflightResult

# Legacy compatibility (now wraps the unified system)
from .integrated_selector import preflight_for_season

__version__ = "3.0.0"
__all__ = [
    'DecisionHistory',
    'DecisionCache',
    'MultiLayerCache',
    'AnalysisResult',
    'ContentKey',
    'AnalysisMode',
    'CacheMetrics',
    'CacheLogger',
    '<PERSON>acheHealthMonitor',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    'TTL<PERSON>oordinator',
    'TTLPolicy',
    'preflight_tv_show',
    'TvPreflightResult',
    'preflight_for_season'
]
