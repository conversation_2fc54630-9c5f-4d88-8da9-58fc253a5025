"""
Unified Cache-Aware TV Show Preflight Analysis System.

This module provides a single entry point for TV show preflight analysis,
consolidating all TV-specific decision logic into one unified interface.
It supports multiple analysis modes, intelligent caching, and seamless
integration with existing Sonarr workflows.

Key Features:
- Single entry point: preflight_tv_show()
- Multiple analysis modes: standard, reliability, hybrid
- Cache-aware with multi-layer caching and GUID reconciliation
- TV-specific features: multi-episode detection, season pack handling
- Comprehensive observability and metrics
- Sonarr workflow compatibility
"""

from __future__ import annotations
import asyncio
import json
import logging
import time
import urllib.parse
import urllib.request
from collections import defaultdict
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Sequence, Set

from .cache import DecisionCache
from .cache_models import AnalysisMode
from .core_analyzer import analyze_metadata
from .indexer_client import extract_newznab_id, fetch_nzb
from .nzb_parser import NZBParser
from .orchestrator_common import choose_best, load_config
from .pack_search import gather_pack_candidates
from .shared_logic import BasicRelease, rank_releases
from .sonarr_client import fetch_releases_for_episode, grab_release, manual_search_episode

logger = logging.getLogger(__name__)


class TvPreflightResult:
    """Result container for TV show preflight analysis."""
    
    def __init__(self, 
                 episodes: List[Dict[str, Any]], 
                 packs: List[Dict[str, Any]], 
                 best: Optional[Dict[str, Any]], 
                 strategy: str, 
                 plan: Dict[str, Any], 
                 stats: Dict[str, Any]):
        self.episodes = episodes
        self.packs = packs
        self.best = best
        self.strategy = strategy
        self.plan = plan
        self.stats = stats
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for backward compatibility."""
        return {
            'episodes': self.episodes,
            'packs': self.packs,
            'best': self.best,
            'strategy': self.strategy,
            'plan': self.plan,
            'stats': self.stats
        }


async def preflight_tv_show(
    series_id: int,
    episode_ids: List[int],
    mode: str = "reliability",
    callback: Optional[Callable] = None,
    config_path: Optional[Path] = None,
    servers_config_path: Optional[Path] = None,
    sonarr_url: Optional[str] = None,
    sonarr_api_key: Optional[str] = None,
    max_candidates: int = 15,
    sample_cap: int = 500,
    accept_threshold: float = 0.50,
    manual_search: bool = True,
    attempt_pack: bool = True,
    pack_category_ids: Optional[Sequence[int]] = None,
    indexer_timeout: float = 5.0,
    season_number: Optional[int] = None,
    specific_episodes: Optional[List[int]] = None
) -> Dict[str, Any]:
    """
    Unified TV show preflight analysis with configurable modes and caching.
    
    This is the single entry point for all TV show preflight analysis,
    replacing fragmented logic with a unified, cache-aware system.
    
    Args:
        series_id: Sonarr series ID
        episode_ids: List of episode IDs to analyze
        mode: Analysis mode ('standard', 'reliability', 'hybrid')
        callback: Optional callback for download triggering
        config_path: Path to preflight configuration
        servers_config_path: Path to servers configuration
        sonarr_url: Sonarr base URL
        sonarr_api_key: Sonarr API key
        max_candidates: Maximum candidates to analyze per episode
        sample_cap: Sample size limit for analysis
        accept_threshold: Threshold for episode coverage
        manual_search: Whether to trigger manual search
        attempt_pack: Whether to attempt season pack analysis
        pack_category_ids: Category IDs for pack search
        indexer_timeout: Timeout for indexer requests
        season_number: Season number (auto-detected if not provided)
        specific_episodes: Specific episode numbers to limit analysis to
    
    Returns:
        Dictionary with analysis results, compatible with existing workflows
    """
    start_time = time.time()
    analysis_mode = AnalysisMode.from_string(mode)
    
    logger.info(f"🎬 Starting TV preflight analysis - Series: {series_id}, Episodes: {len(episode_ids)}, Mode: {mode}")
    
    # Configure analysis behavior based on mode
    use_cache, deduplicate, fresh_checks = _configure_analysis_mode(analysis_mode)
    
    logger.info(f"📋 Analysis configuration - Cache: {use_cache}, Deduplicate: {deduplicate}, Fresh checks: {fresh_checks}")
    
    # Load configuration
    if not config_path:
        config_path = Path('preflight_analyzer/preflight_config.json')
    
    cfg = load_config(config_path)
    indexers_cfg = {i['name']: i for i in cfg.get('indexers', [])}
    
    # Initialize enhanced caching system
    cache_dir = Path('workspace') / 'preflight_cache'
    cache_dir.mkdir(parents=True, exist_ok=True)
    cache_file = cache_dir / 'decision_history.json'
    decision_cache = DecisionCache(cache_file, ttl_seconds=12 * 3600) if use_cache else None
    
    # Load servers configuration
    servers = None
    if servers_config_path:
        from .analyze_release import load_servers
        servers = load_servers(servers_config_path)
    
    # Get Sonarr configuration from config if not provided
    if not sonarr_url or not sonarr_api_key:
        sonarr_cfg = cfg.get('sonarr', {})
        sonarr_url = sonarr_url or sonarr_cfg.get('url', '')
        sonarr_api_key = sonarr_api_key or sonarr_cfg.get('api_key', '')
    
    if not sonarr_url or not sonarr_api_key:
        raise ValueError("Sonarr URL and API key must be provided")
    
    # Fetch episode information from Sonarr
    episode_info = await _fetch_episode_info(sonarr_url, sonarr_api_key, series_id, episode_ids, season_number, specific_episodes)
    
    if not episode_info['episodes']:
        logger.warning("No episodes found for analysis")
        return _create_empty_result()
    
    target_episodes = episode_info['episodes']
    season_num = episode_info['season_number']
    series_title = episode_info['series_title']
    
    logger.info(f"📺 Analyzing {len(target_episodes)} episodes from {series_title} Season {season_num}")
    
    # Trigger manual search if requested
    if manual_search and episode_ids:
        try:
            manual_search_episode(sonarr_url, sonarr_api_key, series_id, episode_ids)
            logger.info("🔍 Manual search triggered for episodes")
        except Exception as e:
            logger.warning(f"Manual search failed: {e}")
    
    # Initialize NZB parser
    parser = NZBParser()
    
    # Perform episode-level analysis
    episode_results = await _analyze_episodes(
        target_episodes, sonarr_url, sonarr_api_key, indexers_cfg, parser, 
        servers, max_candidates, sample_cap, decision_cache, analysis_mode, callback
    )
    
    # Determine strategy based on episode coverage
    strategy_info = _determine_strategy(episode_results, target_episodes, accept_threshold, attempt_pack)
    
    # Perform pack analysis if needed
    pack_results = []
    if strategy_info['need_pack']:
        pack_results = await _analyze_season_packs(
            indexers_cfg, season_num, series_title, pack_category_ids or [], 
            indexer_timeout, parser, servers, sample_cap, decision_cache, 
            analysis_mode, episode_results
        )
    
    # Create final execution plan
    execution_plan = _create_execution_plan(episode_results, pack_results, strategy_info, target_episodes)
    
    # Execute callbacks if provided
    if callback:
        await _execute_callbacks(callback, execution_plan, episode_results, pack_results)
    
    # Calculate final statistics
    total_time = time.time() - start_time
    stats = _calculate_statistics(episode_results, pack_results, total_time, decision_cache)
    
    logger.info(f"✅ TV preflight analysis completed in {total_time:.2f}s - Strategy: {strategy_info['strategy']}")
    
    # Return result in compatible format
    return {
        'episodes': episode_results,
        'packs': pack_results,
        'best': choose_best([r for r in episode_results + pack_results if 'decision' in r]),
        'strategy': strategy_info['strategy'],
        'plan': execution_plan,
        'stats': stats
    }


def _configure_analysis_mode(mode: AnalysisMode) -> tuple[bool, bool, bool]:
    """Configure analysis behavior based on mode."""
    if mode == AnalysisMode.STANDARD:
        return True, True, False  # use_cache, deduplicate, fresh_checks
    elif mode == AnalysisMode.RELIABILITY:
        return False, False, False  # Force fresh analysis
    elif mode == AnalysisMode.HYBRID:
        return True, True, True  # Cache + fresh checks
    else:
        return True, True, False  # Default to standard


async def _fetch_episode_info(sonarr_url: str, sonarr_api_key: str, series_id: int,
                             episode_ids: List[int], season_number: Optional[int],
                             specific_episodes: Optional[List[int]]) -> Dict[str, Any]:
    """Fetch episode information from Sonarr API."""
    # Fetch all episodes for the series
    eps_url = f"{sonarr_url.rstrip('/')}/api/v3/episode"
    q = urllib.parse.urlencode({'seriesId': series_id})
    req = urllib.request.Request(f"{eps_url}?{q}", headers={'X-Api-Key': sonarr_api_key})

    with urllib.request.urlopen(req, timeout=15.0) as resp:  # nosec
        all_episodes = json.loads(resp.read().decode('utf-8', 'replace'))

    # Fetch series information
    series_req = urllib.request.Request(
        f"{sonarr_url.rstrip('/')}/api/v3/series/{series_id}",
        headers={'X-Api-Key': sonarr_api_key}
    )

    try:
        with urllib.request.urlopen(series_req, timeout=10.0) as sresp:  # nosec
            series_obj = json.loads(sresp.read().decode('utf-8', 'replace'))
        series_title = series_obj.get('title', 'Unknown Series')
    except Exception:
        series_title = 'Unknown Series'

    # Filter episodes based on provided IDs
    if episode_ids:
        target_episodes = [ep for ep in all_episodes if ep.get('id') in episode_ids]
    else:
        target_episodes = all_episodes

    # Further filter by season if specified
    if season_number is not None:
        target_episodes = [ep for ep in target_episodes if ep.get('seasonNumber') == season_number]

    # Further filter by specific episode numbers if provided
    if specific_episodes:
        target_episodes = [ep for ep in target_episodes if ep.get('episodeNumber') in specific_episodes]

    # Determine season number from episodes
    season_nums = {ep.get('seasonNumber') for ep in target_episodes if ep.get('seasonNumber') is not None}
    detected_season = season_number or (list(season_nums)[0] if len(season_nums) == 1 else None)

    return {
        'episodes': target_episodes,
        'season_number': detected_season,
        'series_title': series_title
    }


def _create_empty_result() -> Dict[str, Any]:
    """Create empty result for cases with no episodes."""
    return {
        'episodes': [],
        'packs': [],
        'best': None,
        'strategy': 'none',
        'plan': {'episodes': [], 'pack': None},
        'stats': {
            'episodes_total': 0,
            'episodes_covered': 0,
            'episodes_accepted_files': 0,
            'best_files_per_episode': 0,
            'accept_fraction': 0.0,
            'threshold': 0.0,
            'attempt_pack': False,
            'cache_hits': 0,
            'cache_hit_rate': 0.0,
            'total_time': 0.0
        }
    }


async def _analyze_release(meta: Any, servers: Any, sample_cap: int, retention_days: int = 4000):
    """Analyze a single release using the core analyzer."""
    return await analyze_metadata(
        meta,
        servers=servers,
        retention_days=retention_days,
        dry_run=False,
        verbose=False,
        sample_cap=sample_cap
    )


def _extract_additional_episodes_from_title(title: str, target_episodes: List[Dict[str, Any]]) -> Set[int]:
    """
    Extract additional episode IDs from multi-episode file titles.

    Detects patterns like:
    - S01E01-E02 or S01E01-02
    - S01E01E02
    - S01E01.E02.E03
    """
    import re

    # Create mapping from episode number to episode ID
    ep_num_to_id = {
        ep.get('episodeNumber'): ep.get('id')
        for ep in target_episodes
        if ep.get('episodeNumber') and ep.get('id')
    }

    additional_episodes = set()

    # Pattern 1: S##E##-E## (e.g., S01E01-E02, S01E01-02)
    multi_ep_match = re.search(r'S(\d+)E(\d+)-E?(\d+)', title, re.IGNORECASE)
    if multi_ep_match:
        start_ep = int(multi_ep_match.group(2))
        end_ep = int(multi_ep_match.group(3))

        # Add all episodes in range except the first (already counted)
        for ep_num in range(start_ep + 1, end_ep + 1):
            if ep_num in ep_num_to_id:
                additional_episodes.add(ep_num_to_id[ep_num])

        return additional_episodes

    # Pattern 2: S##E##E## (e.g., S01E01E02)
    consecutive_match = re.search(r'S(\d+)E(\d+)E(\d+)', title, re.IGNORECASE)
    if consecutive_match:
        second_ep = int(consecutive_match.group(3))
        if second_ep in ep_num_to_id:
            additional_episodes.add(ep_num_to_id[second_ep])

        return additional_episodes

    # Pattern 3: Multiple E## patterns (e.g., S01E01.E02.E03)
    episode_matches = re.findall(r'E(\d+)', title, re.IGNORECASE)
    if len(episode_matches) > 1:
        # Add all episodes after the first one
        for ep_str in episode_matches[1:]:
            ep_num = int(ep_str)
            if ep_num in ep_num_to_id:
                additional_episodes.add(ep_num_to_id[ep_num])

    return additional_episodes


async def _analyze_episodes(target_episodes: List[Dict[str, Any]], sonarr_url: str,
                           sonarr_api_key: str, indexers_cfg: Dict[str, Any],
                           parser: NZBParser, servers: Any, max_candidates: int,
                           sample_cap: int, decision_cache: Optional[DecisionCache],
                           analysis_mode: AnalysisMode, callback: Optional[Callable]) -> List[Dict[str, Any]]:
    """Analyze individual episodes with parallel processing and caching."""
    episode_results = []

    # Create semaphore to limit concurrent analyses (same as existing system)
    semaphore = asyncio.Semaphore(6)

    logger.info(f"📺 Analyzing {len(target_episodes)} episodes in parallel (max 6 concurrent)")

    for episode in target_episodes:
        ep_id = episode.get('id')
        if not ep_id:
            continue

        # Fetch releases for this episode
        try:
            raw_releases = fetch_releases_for_episode(sonarr_url, sonarr_api_key, ep_id)
        except Exception as e:
            logger.error(f"Failed to fetch releases for episode {ep_id}: {e}")
            episode_results.append({
                'episode_id': ep_id,
                'error': f'Failed to fetch releases: {e}',
                'decision': 'ERROR'
            })
            continue

        # Convert to BasicRelease objects and rank them
        basic_releases = []
        for rel in raw_releases[:max_candidates]:
            guid = rel.get('guid', '')
            indexer_name = rel.get('indexer') or rel.get('indexerName') or ''

            if not indexers_cfg.get(indexer_name):
                continue

            nzb_id = extract_newznab_id(guid)
            if not nzb_id:
                continue

            basic_releases.append(BasicRelease(
                guid=guid,
                title=str(rel.get('title', guid)),
                size=int(rel.get('size', 0)),
                indexer=indexer_name,
                raw=rel
            ))

        # Rank releases using shared logic
        ranked_releases = rank_releases(basic_releases, limit=max_candidates)

        # Prepare parallel analysis tasks
        tasks = []
        for release in ranked_releases:
            idx_cfg = indexers_cfg.get(release.indexer)
            if not idx_cfg:
                continue

            task = _analyze_episode_release_parallel(
                release, idx_cfg, parser, servers, sample_cap, semaphore,
                ep_id, decision_cache, analysis_mode
            )
            tasks.append(task)

        # Execute analyses in parallel
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results for this episode
            for result in results:
                if isinstance(result, Exception):
                    episode_results.append({
                        'episode_id': ep_id,
                        'error': str(result),
                        'decision': 'ERROR'
                    })
                else:
                    episode_results.append(result)

    return episode_results


async def _analyze_episode_release_parallel(release: BasicRelease, idx_cfg: Dict[str, Any],
                                          parser: NZBParser, servers: Any, sample_cap: int,
                                          semaphore: asyncio.Semaphore, ep_id: int,
                                          decision_cache: Optional[DecisionCache],
                                          analysis_mode: AnalysisMode) -> Dict[str, Any]:
    """Analyze a single episode release with concurrency control and caching."""
    async with semaphore:
        try:
            nzb_id = extract_newznab_id(release.guid)
            if not nzb_id:
                return {
                    'guid': release.guid,
                    'indexer': release.indexer,
                    'title': release.title,
                    'episode_id': ep_id,
                    'error': 'No NZB ID',
                    'decision': 'ERROR'
                }

            # Create cache key
            cache_key = f"{release.indexer}:{nzb_id}"

            # Check cache if enabled
            cached_result = None
            if decision_cache and analysis_mode != AnalysisMode.RELIABILITY:
                cached_result = decision_cache.get(cache_key, analysis_mode.value)
                if cached_result:
                    logger.info(f"   💾 {datetime.now().strftime('%H:%M:%S')} Cache hit: {release.title} → {cached_result['decision']} (risk: {cached_result.get('risk', 0.0):.4f})")
                    return {
                        'guid': release.guid,
                        'indexer': release.indexer,
                        'title': release.title,
                        'episode_id': ep_id,
                        'decision': cached_result['decision'],
                        'risk_score': cached_result.get('risk', 0.0),
                        'probe_missing_ratio': cached_result.get('missing_ratio', 0.0),
                        'size': release.size,
                        'cached': True
                    }

            # Perform fresh analysis
            logger.info(f"   🔍 {datetime.now().strftime('%H:%M:%S')} Analyzing: {release.title}")

            raw_nzb = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id, uid=idx_cfg.get('uid'))
            meta = parser.parse_bytes(raw_nzb)

            # Perform additional fresh checks in hybrid mode
            if analysis_mode == AnalysisMode.HYBRID and cached_result:
                # In hybrid mode, we could add additional checks here
                # For now, we'll do the full analysis but log that we had cached data
                logger.debug(f"   🔄 Hybrid mode: performing fresh analysis despite cache hit for {release.title}")

            report = await _analyze_release(meta, servers, sample_cap)
            decision = report.get('decision', 'UNKNOWN')
            risk_score = report.get('risk_score', 0.0)
            missing_ratio = report.get('probe_missing_ratio', 0.0)

            logger.info(f"   ✅ {datetime.now().strftime('%H:%M:%S')} Result: {decision} (risk: {risk_score:.4f}, missing: {missing_ratio:.1%})")

            # Store in cache if enabled
            if decision_cache:
                decision_cache.put(cache_key, decision, report, analysis_mode.value)

            return {
                'guid': release.guid,
                'indexer': release.indexer,
                'title': release.title,
                'episode_id': ep_id,
                'decision': decision,
                'risk_score': risk_score,
                'probe_missing_ratio': missing_ratio,
                'size': release.size,
                'cached': False
            }

        except Exception as e:
            logger.error(f"   ❌ {datetime.now().strftime('%H:%M:%S')} Error analyzing {release.title}: {e}")
            return {
                'guid': release.guid,
                'indexer': release.indexer,
                'title': release.title,
                'episode_id': ep_id,
                'error': str(e),
                'decision': 'ERROR'
            }


def _determine_strategy(episode_results: List[Dict[str, Any]], target_episodes: List[Dict[str, Any]],
                       accept_threshold: float, attempt_pack: bool) -> Dict[str, Any]:
    """Determine the optimal strategy based on episode coverage."""
    # Filter accepted episodes
    accepted_episodes = [r for r in episode_results if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]

    # Group by episode ID and choose best for each episode
    episodes_by_id = defaultdict(list)
    for ep_result in accepted_episodes:
        ep_id = ep_result.get('episode_id')
        if ep_id:
            episodes_by_id[ep_id].append(ep_result)

    # Choose best file per episode and calculate coverage including multi-episode files
    covered_episodes = set()
    best_episodes_per_id = []

    for ep_id, ep_options in episodes_by_id.items():
        # Choose best option for this episode (lowest risk, then largest size)
        best_ep = min(ep_options, key=lambda x: (x.get('risk_score', float('inf')), -x.get('size', 0)))
        best_episodes_per_id.append(best_ep)
        covered_episodes.add(ep_id)

        # Check for multi-episode files
        title = best_ep.get('title', '')
        additional_episodes = _extract_additional_episodes_from_title(title, target_episodes)
        covered_episodes.update(additional_episodes)

    # Calculate coverage
    total_episodes = {ep.get('id') for ep in target_episodes if ep.get('id')}
    coverage_fraction = len(covered_episodes) / len(total_episodes) if total_episodes else 0.0

    logger.info(f"🎯 Episode coverage: {len(covered_episodes)}/{len(total_episodes)} episodes ({coverage_fraction:.1%})")

    # Determine strategy
    if coverage_fraction >= 1.0:
        strategy = 'episodes'
        need_pack = False
        logger.info(f"🎯 100% episode coverage - using individual episodes only")
    elif coverage_fraction >= accept_threshold:
        strategy = 'episodes+pack'
        need_pack = attempt_pack
        missing_count = len(total_episodes) - len(covered_episodes)
        logger.info(f"🎯 {coverage_fraction:.1%} episode coverage - hybrid strategy for {missing_count} missing episodes")
    else:
        strategy = 'pack_only' if attempt_pack else 'episodes'
        need_pack = attempt_pack
        missing_count = len(total_episodes) - len(covered_episodes)
        logger.info(f"🎯 {coverage_fraction:.1%} episode coverage below threshold - preferring season pack")

    return {
        'strategy': strategy,
        'need_pack': need_pack,
        'coverage_fraction': coverage_fraction,
        'covered_episodes': covered_episodes,
        'total_episodes': total_episodes,
        'best_episodes_per_id': best_episodes_per_id
    }


async def _analyze_season_packs(indexers_cfg: Dict[str, Any], season_number: int,
                               series_title: str, pack_category_ids: List[int],
                               indexer_timeout: float, parser: NZBParser, servers: Any,
                               sample_cap: int, decision_cache: Optional[DecisionCache],
                               analysis_mode: AnalysisMode, episode_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Analyze season pack candidates with parallel processing."""
    # Get existing GUIDs to avoid duplicates
    existing_guids = [str(r.get('guid')) for r in episode_results if r.get('guid')]

    # Search for pack candidates
    pack_candidates = gather_pack_candidates(
        indexers_cfg,
        season_number=season_number,
        series_title=series_title,
        pack_category_ids=pack_category_ids,
        indexer_timeout=indexer_timeout,
        parser=parser,
        existing_guids=existing_guids
    )

    if not pack_candidates:
        logger.info("📀 No season pack candidates found")
        return []

    logger.info(f"📀 Analyzing {len(pack_candidates)} season pack candidates in parallel (max 4 concurrent)")

    # Create semaphore for pack analysis (limit to 4 as in existing system)
    pack_semaphore = asyncio.Semaphore(4)

    # Prepare parallel analysis tasks
    pack_tasks = []
    for candidate in pack_candidates:
        idx_cfg = indexers_cfg.get(candidate['indexer'])
        if not idx_cfg:
            continue

        nzb_id = extract_newznab_id(candidate['guid'])
        if not nzb_id:
            continue

        task = _analyze_pack_release_parallel(
            candidate, idx_cfg, parser, servers, sample_cap,
            pack_semaphore, decision_cache, analysis_mode
        )
        pack_tasks.append(task)

    # Execute pack analyses in parallel
    pack_results = []
    if pack_tasks:
        results = await asyncio.gather(*pack_tasks, return_exceptions=True)

        for result in results:
            if isinstance(result, Exception):
                pack_results.append({
                    'error': str(result),
                    'is_pack': True,
                    'decision': 'ERROR'
                })
            else:
                pack_results.append(result)

    return pack_results


async def _analyze_pack_release_parallel(candidate: Dict[str, Any], idx_cfg: Dict[str, Any],
                                        parser: NZBParser, servers: Any, sample_cap: int,
                                        semaphore: asyncio.Semaphore, decision_cache: Optional[DecisionCache],
                                        analysis_mode: AnalysisMode) -> Dict[str, Any]:
    """Analyze a single pack release with concurrency control and caching."""
    async with semaphore:
        try:
            guid = candidate['guid']
            nzb_id = extract_newznab_id(guid)
            if not nzb_id:
                return {
                    'guid': guid,
                    'indexer': candidate['indexer'],
                    'title': candidate['title'],
                    'error': 'No NZB ID',
                    'is_pack': True,
                    'decision': 'ERROR'
                }

            # Create cache key
            cache_key = f"{candidate['indexer']}:{nzb_id}"

            # Check cache if enabled
            if decision_cache and analysis_mode != AnalysisMode.RELIABILITY:
                cached_result = decision_cache.get(cache_key, analysis_mode.value)
                if cached_result:
                    logger.info(f"   💾 {datetime.now().strftime('%H:%M:%S')} Cache hit pack: {candidate['title']} → {cached_result['decision']} (risk: {cached_result.get('risk', 0.0):.4f})")
                    return {
                        'guid': guid,
                        'indexer': candidate['indexer'],
                        'title': candidate['title'],
                        'decision': cached_result['decision'],
                        'risk_score': cached_result.get('risk', 0.0),
                        'probe_missing_ratio': cached_result.get('missing_ratio', 0.0),
                        'size': candidate.get('size'),
                        'is_pack': True,
                        'cached': True
                    }

            # Perform fresh analysis
            logger.info(f"   🔍 {datetime.now().strftime('%H:%M:%S')} Analyzing pack: {candidate['title']}")

            raw_nzb = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id, uid=idx_cfg.get('uid'))
            meta = parser.parse_bytes(raw_nzb)

            report = await _analyze_release(meta, servers, sample_cap)
            decision = report.get('decision', 'UNKNOWN')
            risk_score = report.get('risk_score', 0.0)
            missing_ratio = report.get('probe_missing_ratio', 0.0)

            logger.info(f"   ✅ {datetime.now().strftime('%H:%M:%S')} Pack result: {decision} (risk: {risk_score:.4f}, missing: {missing_ratio:.1%})")

            # Store in cache if enabled
            if decision_cache:
                decision_cache.put(cache_key, decision, report, analysis_mode.value)

            return {
                'guid': guid,
                'indexer': candidate['indexer'],
                'title': candidate['title'],
                'decision': decision,
                'risk_score': risk_score,
                'probe_missing_ratio': missing_ratio,
                'size': candidate.get('size'),
                'is_pack': True,
                'cached': False
            }

        except Exception as e:
            logger.error(f"   ❌ {datetime.now().strftime('%H:%M:%S')} Pack error: {e}")
            return {
                'guid': candidate['guid'],
                'indexer': candidate['indexer'],
                'title': candidate['title'],
                'error': str(e),
                'is_pack': True,
                'decision': 'ERROR'
            }


def _create_execution_plan(episode_results: List[Dict[str, Any]], pack_results: List[Dict[str, Any]],
                          strategy_info: Dict[str, Any], target_episodes: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create final execution plan based on strategy and results."""
    strategy = strategy_info['strategy']
    best_episodes = strategy_info['best_episodes_per_id']

    # Filter accepted packs
    accepted_packs = [r for r in pack_results if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]

    plan = {'episodes': [], 'pack': None}

    if strategy == 'episodes':
        # Use individual episodes only
        plan['episodes'] = [ep['guid'] for ep in best_episodes]
        logger.info(f"📝 Final plan: Using {len(best_episodes)} individual episodes")

    elif strategy == 'episodes+pack' and accepted_packs:
        # Hybrid: episodes + pack for missing episodes
        best_pack = choose_best(accepted_packs)
        plan['episodes'] = [ep['guid'] for ep in best_episodes]
        plan['pack'] = best_pack['guid'] if best_pack else None

        covered = len(strategy_info['covered_episodes'])
        total = len(strategy_info['total_episodes'])
        missing = total - covered
        logger.info(f"📝 Final plan: Using {len(best_episodes)} episodes + season pack for {missing} missing episodes")

    elif strategy == 'pack_only' and accepted_packs:
        # Pack only
        best_pack = choose_best(accepted_packs)
        plan['pack'] = best_pack['guid'] if best_pack else None
        logger.info(f"📝 Final plan: Using season pack only")

    else:
        # Fallback to episodes
        plan['episodes'] = [ep['guid'] for ep in best_episodes]
        covered = len(strategy_info['covered_episodes'])
        total = len(strategy_info['total_episodes'])
        missing = total - covered
        if missing > 0:
            logger.info(f"📝 Final plan: Fallback to {len(best_episodes)} episodes ({missing} will remain missing)")
        else:
            logger.info(f"📝 Final plan: Using {len(best_episodes)} individual episodes")

    return plan


async def _execute_callbacks(callback: Callable, execution_plan: Dict[str, Any],
                           episode_results: List[Dict[str, Any]], pack_results: List[Dict[str, Any]]) -> None:
    """Execute download callbacks for approved releases."""
    try:
        # Execute callback for episodes
        for guid in execution_plan.get('episodes', []):
            # Find the episode result for this GUID
            episode_result = next((r for r in episode_results if r.get('guid') == guid), None)
            if episode_result:
                await callback(episode_result, episode_result.get('episode_id'))
                logger.info(f"   📞 Callback executed for episode: {episode_result.get('title', guid)}")

        # Execute callback for pack
        pack_guid = execution_plan.get('pack')
        if pack_guid:
            pack_result = next((r for r in pack_results if r.get('guid') == pack_guid), None)
            if pack_result:
                await callback(pack_result, None)  # No specific episode ID for packs
                logger.info(f"   📞 Callback executed for pack: {pack_result.get('title', pack_guid)}")

    except Exception as e:
        logger.error(f"   ⚠️ Callback execution failed: {e}")


def _calculate_statistics(episode_results: List[Dict[str, Any]], pack_results: List[Dict[str, Any]],
                         total_time: float, decision_cache: Optional[DecisionCache]) -> Dict[str, Any]:
    """Calculate comprehensive statistics for the analysis run."""
    combined_results = episode_results + pack_results

    # Cache statistics
    cache_hits = sum(1 for r in combined_results if r.get('cached', False))
    total_analyses = len(combined_results)
    cache_hit_rate = (cache_hits / total_analyses) if total_analyses > 0 else 0.0

    # Episode statistics
    episode_ids = {r.get('episode_id') for r in episode_results if r.get('episode_id')}
    accepted_episodes = [r for r in episode_results if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]

    # Group accepted episodes by ID to count unique episodes covered
    episodes_by_id = defaultdict(list)
    for ep in accepted_episodes:
        ep_id = ep.get('episode_id')
        if ep_id:
            episodes_by_id[ep_id].append(ep)

    covered_episodes = len(episodes_by_id)
    best_files_per_episode = len(episodes_by_id)  # One best file per covered episode

    # Pack statistics
    accepted_packs = [r for r in pack_results if r.get('decision') in ('ACCEPT', 'RISKY_LOW_PARITY')]

    stats = {
        'episodes_total': len(episode_ids),
        'episodes_covered': covered_episodes,
        'episodes_accepted_files': len(accepted_episodes),
        'best_files_per_episode': best_files_per_episode,
        'packs_analyzed': len(pack_results),
        'packs_accepted': len(accepted_packs),
        'accept_fraction': covered_episodes / len(episode_ids) if episode_ids else 0.0,
        'cache_hits': cache_hits,
        'cache_hit_rate': cache_hit_rate,
        'total_analyses': total_analyses,
        'total_time': total_time
    }

    if cache_hits > 0:
        logger.info(f"💾 Cache performance: {cache_hits}/{total_analyses} hits ({cache_hit_rate:.1%}) - saved significant analysis time!")

    return stats


# Export the main function and result class
__all__ = ['preflight_tv_show', 'TvPreflightResult']
