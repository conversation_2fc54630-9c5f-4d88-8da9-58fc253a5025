Enhanced Queue-Based Selective Processing for Video Encoder
Support for Multiple Processing Modes (Full, Audio-Only, Skip)

We introduce a new ProcessingMode enum to represent the three modes: Full Encode, Audio-Only Transcode, and Skip (no re-encode). This allows both movies and TV episodes to be processed selectively per item. For example:

from enum import Enum

class ProcessingMode(Enum):
    FULL = "full"            # Full video+audio transcode
    AUDIO_ONLY = "audio"     # Audio-only transcode (video untouched)
    SKIP = "skip"            # Skip re-encoding (direct to final mux)


Full Encode mode uses the existing HandBrake-based transcode for video and audio (the current default behavior). Audio-Only Transcode mode will preserve the video stream and only re-encode the primary audio track to AAC. Skip mode bypasses re-encoding entirely – the file moves straight to the final mux stage (folder 4) without modification.

These modes will be applied per item in the queue. By default, if no mode is specified for an item, it will use Full Encode. Users can override mode per file via the new interactive queue (described below). The internal logic will branch based on ProcessingMode for each file:

FULL: Calls the standard encoding routine (HandBrakeCLI) as before.

AUDIO_ONLY: Executes a new audio transcode routine (using ffmpeg to encode audio to AAC, then mkvmerge to mux it with the original video).

SKIP: Skips encoding and directly relocates the file to the ready_for_final_mux directory.

Each mode is supported for both movies and episodes. We ensure that the pipeline markers and file naming conventions remain consistent, so downstream stages can detect the outcome.

Interactive Queue Interface (--interactive-queue)

We replace the one-by-one confirmation prompts with a CLI-based interactive queue when the --interactive-queue flag is used. Instead of processing each item sequentially with repeated Y/E/S/Q prompts, the script now presents a menu of all pending items and lets the user build a processing queue. Key features of the queue interface:

Multi-Selection: Users can select multiple files at once (e.g. entering 1-3,5 to choose items 1 through 3 and 5). This populates the queue with those items.

Per-Item Mode Selection: After selecting items, the user is prompted to choose a processing mode for each (Full Encode, Audio-Only, or Skip). Each item defaults to Full Encode if no explicit choice is made.

Reordering: Before starting, the user can reorder the queue. The interface shows the selected items and their modes, and the user may input a new ordering (e.g. 2,1,3...) to reprioritize.

Pausing/Resuming: During execution, the queue can be paused after finishing the current item. For example, after an item completes, the script can prompt Press Enter to continue or type 'p' to pause. If paused, the script waits for user input to resume (allowing the user to temporarily halt processing). When ready, the user presses a key to continue with the remaining queue.

This interactive queue is implemented in a new QueueManager class. The QueueManager handles displaying the list of pending items, parsing the selection ranges, and collecting per-item mode choices. It then returns an ordered list of tasks (each task is a tuple of the item reference and its chosen ProcessingMode). For example:

manager = QueueManager(movies_list, episodes_list)
tasks = manager.build_queue_interactively()  # Prompts user for selection, modes, order


The queue interface is entirely text-based (no GUI) and uses clear terminal prompts for usability. For instance, it will list movies and episodes separately with identifying info (movies by title/year, episodes by series and SxxEyy) and then ask for input:

Movies ready for encoding:
 1. The Matrix (1999) [1080p]
 2. Inception (2010) [4K]
Episodes ready for encoding:
 3. 📺 Breaking Bad S01E01 - "Pilot"
 4. 📺 Breaking Bad S01E02 - "Cat's in the Bag..."
 ...
Select items to process (e.g. 1-2,4): 


After selection, it asks for each item’s mode:

Processing mode for "The Matrix (1999)" – [1] Full Encode, [2] Audio-Only, [3] Skip (default 1): 
Processing mode for "Inception (2010)" – [1] Full, [2] Audio-Only, [3] Skip (default 1): 2
...


If any item is given mode 3 (Skip), it will be moved without re-encoding. If mode 2 (Audio-Only) is chosen, only its audio will be transcoded.

Finally, the queue and modes are confirmed and can be reordered:

Queue:
 1. The Matrix (1999) – Full Encode
 2. Inception (2010) – Audio-Only
 3. Breaking Bad S01E01 – Full Encode
Enter new execution order (comma-separated, or press Enter to keep): 


Once confirmed, the script begins processing items in order. After each item, a status is printed and the script can prompt to continue or pause. This design replaces repetitive confirmations with a single batch confirmation, greatly streamlining user interaction.

Implementation: QueueManager Class

We add a new QueueManager (in a new module or within 04_video_encoder.py) to encapsulate queue logic:

class QueueManager:
    def __init__(self, movies: List[MovieInfo], episodes: List[EpisodeInfo]):
        self.items = []  # List of tuples (obj, type_str)
        for m in movies:
            self.items.append((m, "movie"))
        for e in episodes:
            self.items.append((e, "episode"))
    def display_items(self):
        # Print numbered list of pending movies and episodes
        ...
    def parse_selection(self, selection_str: str) -> List[int]:
        # Parse a string like "1-3,5" into a list of indices
        ...
    def choose_modes(self, indices: List[int]) -> List[Tuple[Any, ProcessingMode]]:
        # For each selected index, prompt for mode and build tasks list
        ...


Selection Input: The manager displays all ready-to-encode items (movies first, then episodes) with an index. The user input (e.g. 1-3,5) is parsed into a list of selected indices. Ranges like 1-3 are expanded. We also accept keywords like all to select everything. Invalid indices are handled with a warning and re-prompt if necessary.

Mode Assignment: For each chosen item, QueueManager.choose_modes will ask the user to pick a processing mode. We use numeric or letter shortcuts (1/2/3 or F/A/S) for convenience. If the user just hits Enter, it defaults to Full Encode. The resulting task list contains each item paired with its ProcessingMode.

Reordering: The manager then shows the queue summary and asks if the user wants to reorder. If a new order is provided, it reorders the task list accordingly. Otherwise, the current order is kept. At this point the queue is finalized for execution.

The main script then iterates through the prepared queue. Pseudocode for execution loop:

for (obj, mode) in tasks:
    try:
        if mode == ProcessingMode.FULL:
            success = (encoder.process_movie(obj) if type=="movie" 
                       else encoder.process_episode(obj))
        elif mode == ProcessingMode.AUDIO_ONLY:
            success = (encoder.process_movie_audio_only(obj) if type=="movie" 
                       else encoder.process_episode_audio_only(obj))
        elif mode == ProcessingMode.SKIP:
            success = (encoder.skip_movie(obj) if type=="movie" 
                       else encoder.skip_episode(obj))
        ...
    except KeyboardInterrupt:
        # handle pause request if any (set via input in loop)
        ...


Between items, we check for pause. In practice, we can simply prompt the user after each file completes: e.g., "Press Enter to process the next item, or type 'p' to pause." If the user types p, we break out and wait for a resume command (or allow resuming by pressing Enter later). This effectively pauses the queue without exiting the program. When resumed, processing continues with the remaining items. (Under the hood, pause/resume can be handled by catching a special input or exception, but conceptually it just waits for user confirmation to proceed.)

The queue interface is entirely CLI (no graphical UI), using clear text prompts and status messages. This keeps the implementation simple and suitable for terminal use. Progress and completion of each item will be logged to console as before. For example, after finishing an item, it might log ✓ Encoding complete for Inception (2010) or ✅ Successfully encoded episode ... as in the current implementation.

Audio-Only Transcode Mode Implementation

For Audio-Only mode, we integrate ffmpeg and mkvmerge to replace the primary audio track with AAC while keeping the original video. The process for a movie is:

Prepare Input/Output Paths: Identify the stage 3 processed MKV file (video + original audio) and determine the output file path in stage 4. We reuse the existing logic for output paths so that the file naming remains consistent. For example, get_output_path() yields a filename ending in .encoded.mkv in the 4_ready_for_final_mux directory. We use that as the target output for the muxed result.

Extract & Encode Audio: Use ffmpeg to extract the primary audio from the stage3 MKV and encode it to AAC. We read the ffmpeg executable path from settings (see next section). The ffmpeg command is constructed to take the first audio track and output an AAC file. For instance:

ffmpeg -i "<input>.mkv" -map 0:a:0 -c:a aac -b:a 160k -ac 2 "<temp_audio>.aac"


This command picks the first audio stream (-map 0:a:0), encodes it to AAC at 160 kbps stereo (assuming the pipeline default is AAC 2.0 at ~160 kbps), and writes out a temporary .aac file. We chose 160k and 2 channels as per the existing audio settings (e.g. default audio mixdown stereo @ 160 kbps in config).

Mux Video + New Audio: Next, we use mkvmerge to mux the original video stream with the newly encoded AAC audio. We exclude the original audio from the input. For example:

mkvmerge -o "<output>.encoded.mkv" -d 0 -A "<input>.mkv" "<temp_audio>.aac"


Here -d 0 selects the video track 0 from the input MKV, -A excludes all audio from the input (so we don’t carry the original track), and then we add the AAC file as the new audio track. This produces a new MKV in the stage4 output directory that has the original video and the new AAC audio. Any subtitles or chapters present in the processed MKV would also be carried unless explicitly excluded. (In our case, the processed MKV typically has no embedded subs, since stage3 extracted them already, so the final file will just have video+audio.)

Markers and Cleanup: We treat this output as equivalent to a fully encoded file. That means we create an .encoded marker in the output directory and a .subtitle_processing_pending marker to signal that subtitles still need to be processed/muxed. We then clean up stage3 artifacts:

Remove the .mkv_complete (and any .mkv_processing) marker from the stage3 source directory, since we are done with encoding for this item.

Copy Original Audio (optional): In the Full Encode path, after encoding, the script copies the largest original audio track from stage3 to stage4 for reference. In Audio-Only mode, the original primary audio is essentially being replaced by AAC in the final file. However, since the original audio (e.g. a high-bitrate DTS-HD or TrueHD track) might be useful for archival or manual muxing, we can still copy it to stage4. The existing copy_largest_audio_file() utility will find the largest track file in the stage3 _Processed_Audio folder and copy it over. This likely corresponds to the main audio track file extracted earlier (stage3 extracts all audio tracks to _Processed_Audio). We invoke this after muxing, similar to the Full Encode flow. If for some reason the main track wasn’t extracted (stage3 might have left it embedded and only extracted other tracks), then the _Processed_Audio folder might be empty or contain other tracks. In that case, either nothing is copied or the largest other track (e.g. a secondary audio) would be copied. In general, this step ensures parity with the Full Encode workflow, preserving the highest-quality audio externally.

Remove the stage3 processed MKV and audio files. We call the same cleanup routine that Full Encode uses to delete the .processed.mkv source and the _Processed_Audio folder in stage3. This prevents duplicate data from lingering once the file has moved to stage4.

For TV episodes in Audio-Only mode, the steps are analogous. The main difference is path construction: episodes in stage3 reside under the series/season folders (e.g. .../3_mkv_cleaned_subtitles_extracted/tv_shows/1080p/Series Name (Year)/Season 01/S01E01.processed.mkv). We use get_episode_output_path() to determine the stage4 destination (which will be in 4_ready_for_final_mux/tv_shows/.../Season XX/Series_SxxEyy...encoded.mkv). Then:

Run ffmpeg on the episode’s processed MKV to get AAC audio (-map 0:a:0 will usually pick the sole audio track of the episode file).

Run mkvmerge to mux video and AAC into the output .encoded.mkv.

Mark the episode as encoded. The episode batch workflow uses a different marker naming (it calls set_episode_encoding_markers to mark start and completion). We will mark it analogous to a successful encode. For consistency with movies, we can drop a .encoded file in the output season folder or use the episode metadata functions. A simple approach is to treat it like movies: create an .encoded marker file in the Season folder with details. This ensures that the filesystem-first state manager knows the episode is encoded.

Clean up the stage3 Season folder: remove the .processed.mkv (which we moved) and the _Processed_Audio directory. (Subtitles in _Processed_Subtitles remain for final muxing later.)

After Audio-Only processing, the pipeline will have an output file in folder 4 with a .encoded.mkv extension containing original video + AAC audio. A marker file .encoded in that directory carries metadata (completion time, etc.), and .subtitle_processing_pending indicates that subtitles (if any) still need integration. At this point, the item is in the same state as a fully encoded file from HandBrake’s perspective, except the video is unmodified. The orchestrator or subsequent stage will handle subtitles (OCR or mux) and then final mux packaging.

Skip Mode Implementation (Direct to Final Mux Stage)

Skip mode is the simplest: it assumes the file is already in a desirable format, so we skip transcoding entirely. Implementation steps for skip:

Move File to Stage4: We take the stage3 processed video file and move it to the stage4 "ready for final mux" directory. We use the same naming convention for consistency. For example, if a movie’s processed file was Movie Title (Year).processed.mkv in stage3, we move/rename it to Movie Title (Year).encoded.mkv in the appropriate stage4 folder. We ensure the resolution subdirectory and movie folder exist in stage4 (the get_output_path() logic already creates them). Essentially, we are promoting the stage3 MKV to stage4 as if it were encoded.

Markers: We then create the .encoded marker in the stage4 directory to indicate the encoding stage is done. Even though we didn't re-encode, we mark it as encoded so that the pipeline knows to proceed. We include relevant data (we can note that video was copied original, and audio codec may be whatever it was). We also set .subtitle_processing_pending in stage4 to signal that subtitles still need handling (since skip doesn’t alter subtitles). Then we remove the stage3 markers .mkv_complete (and any .mkv_processing if present) for that item, since it is no longer awaiting encoding.

No Transcoding Needed: Because we are not invoking HandBrake or ffmpeg for skip, the original video and audio stay intact. For example, if a movie had a 4K HEVC video and a DTS-HD audio track and we choose Skip, those will remain as-is in the final file. (This might result in a larger file or non-AAC audio in Plex, but skip is presumably user’s choice for specific cases.)

Cleanup Stage3: We perform the same post-encoding cleanup on stage3. Since we moved the .processed.mkv out, it’s gone from stage3. We can remove any leftover _Processed_Audio folder. In the Full Encode path, after encoding, the code deletes the stage3 processed file and audio folder. We do the same here for consistency: call the cleanup routine on the stage3 directory. This will remove the now-empty processed file (or log a warning if not found) and the audio folder. (Subtitles folder is not removed – those are needed for muxing later.)

Original Audio Handling: In skip mode, since we did not create a new AAC track, the final file still contains the original primary audio (and only that track, as the stage3 processed MKV typically has only the main track). We don’t need to add any audio. Also, there’s no need to copy any audio to stage4 for backup, because the final file itself has the full-quality audio. (In fact, copying would duplicate the audio unnecessarily.) Therefore, we will likely skip the copy_largest_audio_file step in skip mode. (Alternatively, we could still copy the audio file from stage3 _Processed_Audio if it exists, but since that track is already in the output MKV, it’s redundant. The pipeline’s purpose for copying was to preserve a higher-quality track that was about to be dropped; here nothing is dropped.)

For TV episodes, skip mode similarly moves the episode’s .processed.mkv from the stage3 series/season folder to the stage4 tv_shows structure (season folder), naming it with .encoded.mkv. We then mark it as encoded (for episodes, this could be done by creating an encoding_complete or .encoded marker). We clear the .mkv_complete marker in the stage3 folder for that episode. Finally, remove the now-empty processed file reference and audio folder from stage3. The episode’s original audio remains in the file. Subsequent steps (subtitle OCR or final mux) will proceed as if it were encoded.

By implementing skip this way, we effectively tell the system the item is finished encoding and ready for final mux. The orchestrator or next stage will find the file in 4_ready_for_final_mux with an .encoded marker (and also a .subtitle_processing_pending marker if subtitles need work) and handle it accordingly. This fulfills the “skip to Folder 4” requirement.

Configuration of External Tool Paths (ffmpeg, ffprobe, mkvmerge)

We leverage the existing settings.ini to obtain paths for external tools. In settings.ini’s [Executables] section, paths for mkvmerge, mkvextract, ffprobe, etc., are already defined. We will add an entry for ffmpeg (if not already present) so it might look like:

[Executables]
mkvmerge_path = C:/Program Files/MKVToolNix/mkvmerge.exe  
mkvextract_path = C:/Program Files/MKVToolNix/mkvextract.exe  
ffprobe_path = C:/ffmpeg/bin/ffprobe.exe  
ffmpeg_path = C:/ffmpeg/bin/ffmpeg.exe        # (new entry for ffmpeg)


Our code will load these paths at runtime. We can use Python’s configparser to read settings.ini. For example:

import configparser
config = configparser.ConfigParser()
config.read('settings.ini')
ffmpeg_path = config['Executables'].get('ffmpeg_path', 'ffmpeg')
ffprobe_path = config['Executables'].get('ffprobe_path', 'ffprobe')
mkvmerge_path = config['Executables'].get('mkvmerge_path', 'mkvmerge')


This will default to the bare command name if the ini path isn’t provided (assuming the tool is in PATH). We then store these in the encoder, e.g. self.ffmpeg_path = Path(ffmpeg_path).

We also extend the CLI to allow overriding these paths. New arguments --ffmpeg-path, --ffprobe-path, and --mkvmerge-path are added. If the user provides these, they take precedence over the config. For instance, if someone runs:

04_video_encoder.py --interactive-queue --ffmpeg-path "/usr/local/bin/ffmpeg"


the script will use that ffmpeg binary. Overrides are applied like:

if args.ffmpeg_path:
    encoder.ffmpeg_path = Path(args.ffmpeg_path)
if args.ffprobe_path:
    encoder.ffprobe_path = Path(args.ffprobe_path)
...


This approach ensures flexibility: by default we rely on the configured paths in settings.ini (which centralizes tool locations for the whole pipeline), but power users can override via CLI if needed.

We continue to use the HandBrakeCLI path from either settings or the default tools directory. (The existing code already handles --handbrake-path argument and default fallback.)

Additionally, we modify internal calls to use these paths. For example, in the _get_actual_duration() method, instead of calling "ffprobe" directly, we call self.ffprobe_path. This way, if the ffprobe path is configured (or overridden), it will be used. The same applies when constructing ffmpeg and mkvmerge commands for audio processing – we refer to self.ffmpeg_path and self.mkvmerge_path.

By integrating the settings.ini paths, we keep tool invocations flexible and avoid hardcoding paths. This is particularly important for cross-platform or custom installations.

Maintaining Automation and Backwards Compatibility

We ensure that these enhancements do not break existing automated flows:

The --auto-confirm flag still causes the script to process all items automatically in batch (Full Encode for everything, as before). The new interactive queue is bypassed in this mode. In practice, if --auto-confirm is used, we will not invoke the queue prompt. The logic in main() can check: if --interactive-queue is not set (or if auto-confirm is set), it will proceed with the old batch mode. If both flags are provided (which would be contradictory), we can choose to honor auto-confirm and ignore the queue, or vice versa. The safer approach is to prefer non-interactive behavior when auto-confirm is on. We can print a warning or simply skip the interactive prompt. This guarantees headless operation for automation remains unaffected.

Other flags like --movies-only, --tv-only, --all continue to work. In fact, they can be used in conjunction with --interactive-queue to limit what the queue will show. For example, --movies-only --interactive-queue would launch the queue UI but only list movies. Internally, we determine the content scope first, then if interactive-queue is enabled, we display only that subset in the queue manager. If no content flags are given, the interactive menu (movies/tv/both) can still be shown or we can default to both. We have flexibility here: since --interactive-queue implies user interaction, we could skip the initial content-type prompt and just present all pending media by default. In our implementation, we’ll likely allow combination: e.g. if --movies-only and --interactive-queue are both set, queue manager will only list movies (same for episodes).

The existing sequential prompt functions (prompt_user_confirmation, etc.) remain in place for normal interactive mode. If the user does not use --interactive-queue, the script can still operate exactly as before (asking Y/N per item). The new queue interface is activated only when requested. This means minimal change for users who prefer the old workflow or for automated pipeline runs triggered by the orchestrator.

We preserve the internal state management. The .encoded and .subtitle_processing_pending markers written by our new modes are the same markers that the pipeline already expects in folder 4 for a completed encode. Similarly, by clearing the .mkv_complete marker in folder 3, we ensure the item won’t be picked up again as pending encoding. The FilesystemFirstStateManager’s stage discovery will recognize that the item moved to the next stage (final mux pending). For episodes, marking as encoded/final_mux_pending integrates with the state as well (the snippet from stage3 processing shows next_status set to final_mux_pending for episodes after subs extraction, indicating readiness for final packaging).

After encoding or skipping, we keep subtitle handling workflow intact. We leave .subtitle_processing_pending markers so that the Subtitle/OCR stage (Pipeline 05, presumably) knows to process those. We do not modify subtitles in audio-only or skip modes; any external subtitle files extracted in stage3 remain to be handled. In skip mode, if an item had image-based subtitles, the pipeline would still require OCR just as if it had been encoded. Our markers ensure those steps happen. (If no subtitles, the final mux stage can proceed once it sees .encoded present, perhaps immediately or after poster generation.)

In summary, these changes deliver the requested functionality: an interactive queue allowing batch selection and mode choices, plus specialized handling for audio-only and skip scenarios. The code is integrated such that if the user opts in, they get the new behavior; otherwise, the pipeline operates as it did, maintaining compatibility with existing automation and state tracking.

All tool usage is configured through settings.ini for flexibility, and the use of ffmpeg + mkvmerge for audio transcode fulfills the requirement of replacing the main audio track with AAC while preserving video quality. The final outcome is that the video encoder script can now selectively transcode or skip files, significantly improving efficiency for use cases like keeping original video but compressing audio, or skipping re-encode for already compliant files.

Sources:

Pipeline code references for marker handling and cleanup: setting .encoded and .subtitle_processing_pending markers in stage4, and cleaning up stage3 processed files and audio.

Settings configuration for external tools (mkvmerge, ffprobe, etc.).

Existing CLI prompt flow (Y/E/S/Q per item) to be superseded by the queue.

Stage3 output conventions for episodes (processed MKV naming and location).