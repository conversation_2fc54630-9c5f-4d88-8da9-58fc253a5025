# PlexAutomator Internal Scripts

This folder contains utility and maintenance scripts for the PlexAutomator system.

## Files

### `sabnzbd_post_process.py`
**Purpose:** Bridge script that connects SABnzbd to the PlexMovieAutomator pipeline.

**What it does:**
1. Gets called by SABnzbd when a download finishes
2. Logs the download completion details
3. Triggers the pipeline's Stage 02 (download and organize)
4. Processes the completed download immediately

**SABnzbd Configuration:**
- **Scripts Folder:** `C:\Users\<USER>\Videos\PlexAutomator\scripts`
- **Script:** `sabnzbd_post_process.py`
- **Parameters:** SABnzbd automatically passes 7 parameters

### `cache_maintenance.py`
**Purpose:** Cache maintenance and management utility for the preflight analysis system.

**What it does:**
1. **Cleanup:** Remove expired cache entries
2. **Report:** Generate comprehensive performance reports
3. **Migrate:** Migrate data from legacy cache formats
4. **Stats:** Display detailed cache statistics
5. **Health:** Perform cache health checks

**Usage Examples:**
```bash
# Show cache statistics
python _internal/scripts/cache_maintenance.py stats workspace/preflight_cache/cache/analysis_cache.db

# Cleanup expired entries
python _internal/scripts/cache_maintenance.py cleanup workspace/preflight_cache/cache/analysis_cache.db

# Generate performance report
python _internal/scripts/cache_maintenance.py report workspace/preflight_cache/cache/analysis_cache.db --output cache_report.txt

# Health check
python _internal/scripts/cache_maintenance.py health workspace/preflight_cache/cache/analysis_cache.db
```

## How It Works

```
SABnzbd Download Complete
         ↓
sabnzbd_post_process.py
         ↓
Triggers Pipeline Stage 02
         ↓
Organizes movie to workspace/2_downloaded_and_organized
         ↓
Ready for next pipeline stages
```

## Logs

Post-processing logs are written to: `logs/sabnzbd_post_process.log`

## Testing

To test the script manually:
```bash
python scripts/sabnzbd_post_process.py "test_folder" "test.nzb" "Test Movie" "123" "movies" "group" "0"
```
