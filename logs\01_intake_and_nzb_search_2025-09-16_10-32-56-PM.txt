=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-16 22:32:56
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_10-32-56-PM.txt
==================================================

[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_10-32-56-PM.txt
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 22:32:56
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,126 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,128 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,128 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,129 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,129 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,129 - interactive_pipeline_01 - INFO -    📊 Loaded 8 existing movie records
[2025-09-16 22:32:56] [STDERR] [+0:00:00] 2025-09-16 22:32:56,129 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:56] [STDOUT] [+0:00:00]   4. Quit
[2025-09-16 22:32:56] [STDOUT] [+0:00:00] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:32:57] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-16 22:32:57] [STDOUT] [+0:00:01] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 📁 Loaded 12 tv_shows from C:\Users\<USER>\Videos\PlexAutomator\new_tv_requests.txt
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] ======================================================================
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 📺 TV Shows Available for Processing:
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] ======================================================================
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    1. Ed, Edd n Eddy (1999)                    📚 Complete Series        
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    2. Adventure Time (2010)                    📚 Complete Series        
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    3. The Saddle Club (2003)                   📚 Complete Series        
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    4. Samurai Jack (2001) S01                  📀 Season S01             
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    5. Steven Universe (2013) S02               📀 Season S02             
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    6. Futurama (1999) S01                      📀 Season S01             
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    7. The Powerpuff Girls (1998)               📚 Complete Series        
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    8. Regular Show (2010) S08E31               📺 Episode S08E31         
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    9. Dexter's Laboratory (1996) S01E01, S01E05, S01E12 📺 Multi-Episodes S01E01, S01E05, S01E12
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   10. Teen Titans (2003) S02E03, S02E07, S02E13 📺 Multi-Episodes S02E03, S02E07, S02E13
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   11. Johnny Bravo (1997) S01E01, S03E15       📺 Multi-Episodes S01E01, S03E15
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   12. Ben 10 (2005) S01E01, S02E13, S04E21     📺 Multi-Episodes S01E01, S02E13, S04E21
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 📊 Legend:
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    📺 Episode    - Single episode download
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    📀 Season     - Full season download (all episodes)
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]    📚 Series     - Complete series (all seasons)
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 📝 Selection Options:
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   • Single: Enter number (e.g., '3')
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   • All: Enter 'all' or 'a'
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   • None: Enter 'none' or 'n' to skip
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:00] [STDOUT] [+0:00:03]   • Quit: Enter 'quit' or 'q'
[2025-09-16 22:33:00] [STDOUT] [+0:00:03] 
[2025-09-16 22:33:12] [STDOUT] [+0:00:15] 
[2025-09-16 22:33:12] [STDOUT] [+0:00:15] ✅ Selected 1 TV shows:
[2025-09-16 22:33:12] [STDOUT] [+0:00:15] 
[2025-09-16 22:33:12] [STDOUT] [+0:00:15]     1. 📚 Regular Show (2010) S08E31
[2025-09-16 22:33:12] [STDOUT] [+0:00:15] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 📺 Processing 1 selected TV shows...
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] ============================================================
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDERR] [+0:00:19] 2025-09-16 22:33:15,332 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 📍 Progress: 1/1
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 📺 Processing: Regular Show (2010) S08E31
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19]    🎯 Request Type: Specific Episodes
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDOUT] [+0:00:19]    📺 Target: Season 8, Episode 31
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDERR] [+0:00:19] 2025-09-16 22:33:15,333 - interactive_pipeline_01 - INFO - Processing TV show: Regular Show (2010) S08E31 (specific_episodes)
[2025-09-16 22:33:15] [STDERR] [+0:00:19] 2025-09-16 22:33:15,498 - _internal.src.metadata_fetcher - INFO - TMDb TV search with year 2010: 1 results
[2025-09-16 22:33:15] [STDERR] [+0:00:19] 2025-09-16 22:33:15,601 - _internal.utils.fuzzy_matching - INFO - Found 1 exact matches for 'Regular Show', prioritizing them
[2025-09-16 22:33:15] [STDERR] [+0:00:19] 2025-09-16 22:33:15,804 - _internal.src.metadata_fetcher - ERROR - Error during TV fuzzy matching for 'Regular Show': EnhancedFuzzyMatchingConfig.get_year_tolerance() missing 1 required positional argument: 'content_type'
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] ✅ Found metadata: Regular Show (2010)
[2025-09-16 22:33:15] [STDOUT] [+0:00:19] 
[2025-09-16 22:33:15] [STDERR] [+0:00:19] 2025-09-16 22:33:15,851 - interactive_pipeline_01 - INFO - Successfully found TV metadata for: Regular Show
[2025-09-16 22:33:16] [STDERR] [+0:00:20] 2025-09-16 22:33:16,172 - interactive_pipeline_01 - INFO - 🧪 Skipping creation of season pack blocking profile (preflight handles pack decisions)
[2025-09-16 22:33:16] [STDERR] [+0:00:20] 2025-09-16 22:33:16,173 - interactive_pipeline_01 - INFO - 📺 TV Quality Strategy (ADAPTIVE): Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-16 22:33:16] [STDERR] [+0:00:20] 2025-09-16 22:33:16,173 - interactive_pipeline_01 - INFO - 📺 Year-based preference hint: 2010 (used for internal logic, not restrictions)
[2025-09-16 22:33:16] [STDERR] [+0:00:20] 2025-09-16 22:33:16,173 - interactive_pipeline_01 - INFO - Searching Sonarr for: Regular Show 2010
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,461 - interactive_pipeline_01 - INFO - Selected TV show: Regular Show (2010) | tvdbId=188401 | alternatives: [{'title': 'The Ricky Gervais Show', 'year': 2010, 'tvdbId': 142581, 'score': 312}, {'title': 'Fran Drescher Show (2010)', 'year': 2010, 'tvdbId': 404559, 'score': 306}, {'title': 'Moyashimon (2010)', 'year': 2010, 'tvdbId': 178391, 'score': 306}]
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,466 - interactive_pipeline_01 - INFO - 📁 Matched existing Sonarr root folder: E:\
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,466 - interactive_pipeline_01 - INFO - 📋 Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,466 - interactive_pipeline_01 - INFO - 📺 Adding TV show to Sonarr with 1 quality profile(s): Regular Show
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,468 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 6...
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,507 - interactive_pipeline_01 - INFO -    ✅ Added: Regular Show (Series ID 364, Profile 6)
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,507 - interactive_pipeline_01 - INFO -    📺 Configuring monitoring for 1 specific episodes
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,507 - interactive_pipeline_01 - INFO -    🔄 Attempt 1/15: Fetching episodes for series 364
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,509 - interactive_pipeline_01 - INFO -    📊 Found 0 episodes in Sonarr
[2025-09-16 22:33:18] [STDERR] [+0:00:22] 2025-09-16 22:33:18,509 - interactive_pipeline_01 - INFO -    ⏳ Episodes not yet populated, waiting 4s (attempt 1/15)
[2025-09-16 22:33:22] [STDERR] [+0:00:26] 2025-09-16 22:33:22,513 - interactive_pipeline_01 - INFO -    🔄 Attempt 2/15: Fetching episodes for series 364
[2025-09-16 22:33:22] [STDERR] [+0:00:26] 2025-09-16 22:33:22,525 - interactive_pipeline_01 - INFO -    📊 Found 277 episodes in Sonarr
[2025-09-16 22:33:22] [STDERR] [+0:00:26] 2025-09-16 22:33:22,525 - interactive_pipeline_01 - INFO -    📋 Found 277 episodes in series
[2025-09-16 22:33:22] [STDERR] [+0:00:26] 2025-09-16 22:33:22,525 - interactive_pipeline_01 - INFO -    🎯 Target episodes: {(8, 31)}
[2025-09-16 22:33:22] [STDERR] [+0:00:26] 2025-09-16 22:33:22,526 - interactive_pipeline_01 - WARNING - None of the requested episodes were found in the series
[2025-09-16 22:33:22] [STDERR] [+0:00:26] 2025-09-16 22:33:22,526 - interactive_pipeline_01 - INFO - 📏 Max episode size threshold configured: 40.0 GB
[2025-09-16 22:33:27] [STDERR] [+0:00:31] 2025-09-16 22:33:27,533 - interactive_pipeline_01 - INFO - Episode size enforcement: no oversized items detected
[2025-09-16 22:33:27] [STDERR] [+0:00:31] 2025-09-16 22:33:27,534 - interactive_pipeline_01 - INFO - ✅ Season pack evaluation handled by preflight analyzer
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 📥 Queued "Regular Show (2010)" for download...
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDERR] [+0:00:31] 2025-09-16 22:33:27,535 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T22:33:27.535930", "event": "download_queued", "job_id": "sonarr_364_series", "title": "Regular Show (2010)", "source": "sonarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "sonarr_id": 364, "quality": "Unknown"}
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 📊 TV show queued for download: Regular Show (2010)
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31]    🔬 Real-time tracking: sonarr_3...
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31]    📺 Series-wide tracking enabled
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDERR] [+0:00:31] 2025-09-16 22:33:27,536 - interactive_pipeline_01 - INFO - Telemetry job started: sonarr_364_series for series 364
[2025-09-16 22:33:27] [STDOUT] [+0:00:31]    📺 Configured for: S08E31 only
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 🔍 Alternative candidate matches (top scoring):
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31]    • The Ricky Gervais Show (2010) tvdb:142581 score:312
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31]    • Fran Drescher Show (2010) (2010) tvdb:404559 score:306
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31]    • Moyashimon (2010) (2010) tvdb:178391 score:306
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDERR] [+0:00:31] 2025-09-16 22:33:27,537 - interactive_pipeline_01 - INFO - ✅ Successfully added TV show to Sonarr: Regular Show
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 🤔 Download Strategy Choice for: Regular Show
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] Choose how you want to handle downloads for this show:
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 2. ⚡ Sonarr Auto-Grab - Let Sonarr immediately search and grab based on quality profiles
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 3. ⏭️  Skip - Add to Sonarr but don't start any downloads yet
[2025-09-16 22:33:27] [STDOUT] [+0:00:31] 
[2025-09-16 22:33:40] [STDOUT] [+0:00:44] 🔬 Using Preflight Analysis for Regular Show
[2025-09-16 22:33:40] [STDOUT] [+0:00:44] 
[2025-09-16 22:33:40] [STDERR] [+0:00:44] 2025-09-16 22:33:40,686 - interactive_pipeline_01 - INFO - User selected preflight analysis for: Regular Show
[2025-09-16 22:33:40] [STDOUT] [+0:00:44] 🔎 Preflight analyzing 1 season(s): [8]
[2025-09-16 22:33:40] [STDOUT] [+0:00:44] 
[2025-09-16 22:33:40] [STDOUT] [+0:00:44]    Season 8: Episodes [31]
[2025-09-16 22:33:40] [STDOUT] [+0:00:44] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 🎯 Analyzing Season 8...
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 🆕 No existing decision found for Season 8, running fresh analysis
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    🐛 DEBUG: Filtering 0 accepted packs for season 8
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    🐛 DEBUG: Found 0 season-specific packs
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    🐛 DEBUG: No season-specific packs found, season_pack = None
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    ➤ Season 8: 0/0 episodes covered (0.00%) with 0 files, strategy=none
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    📋 Season 8: 0 downloads handled by immediate callback
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📊 Combined Results: 0 total episodes analyzed, 0 acceptable episodes + 0 season packs found
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📊 Queue Status: 0 items queued across 1 season(s) (telemetry tracking enabled)
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📝 Season 8 decision saved: workspace\preflight_decisions\tv_shows\Regular_Show_S08.json
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] ⚠️ No acceptable releases found in preflight analysis
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,733 - interactive_pipeline_01 - INFO - ⚠️ No releases queued by preflight - enabling basic monitoring for future episodes
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] ⚠️ No suitable releases found - enabling basic monitoring for future episodes
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,744 - interactive_pipeline_01 - INFO -    ✅ Enabled basic monitoring - will detect future releases
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    ✅ Basic monitoring enabled for future episodes/seasons
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,750 - interactive_pipeline_01 - INFO - Stored enhanced TV metadata for: Regular Show (2010)
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] ============================================================
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📊 TV Download Summary (compact)
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] ============================================================
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] • Regular Show (2010) S08E31 → Unknown
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] ============================================================
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📊 Processing Complete!
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] ============================================================
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 🎬 Movies processed: 0
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📺 TV shows processed: 1
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📊 Total content processed: 1
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 🔍 Verifying 1 downloads actually started...
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    This replaces guesswork with real verification!
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,751 - interactive_pipeline_01 - INFO - 🔄 Starting real-time download monitoring (interval: 5s)
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 🔎 Verifying download in queue for "Regular Show (2010)" (attempt 1/6)...
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,929 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'Regular Show (2010)' (ID: None)
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,929 - interactive_pipeline_01 - INFO - 🔧 Intelligent Fallback System initialized
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,929 - interactive_pipeline_01 - INFO -    📁 Preflight decisions: workspace\preflight_decisions
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,929 - interactive_pipeline_01 - INFO -    🎬 Radarr URL: http://localhost:7878
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,929 - interactive_pipeline_01 - INFO -    🔑 API Key configured: ✅
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,930 - interactive_pipeline_01 - INFO -    📊 Telemetry integration: ✅
[2025-09-16 22:33:48] [STDERR] [+0:00:52] 2025-09-16 22:33:48,930 - interactive_pipeline_01 - INFO - 🛡️ Intelligent fallback system initialized with telemetry integration
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📊 Real-time telemetry monitoring started
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 📄 Telemetry dashboard log: logs\telemetry_dashboard_2025-09-16_10-33-48-PM.txt
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52]    (Dashboard output will be written to separate file to keep main log clean)
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 🔎 Verifying download in queue for "Regular Show (2010)" (attempt 2/6)...
[2025-09-16 22:33:48] [STDOUT] [+0:00:52] 
[2025-09-16 22:33:59] [STDERR] [+0:01:02] 2025-09-16 22:33:59,111 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'Regular Show (2010)' (ID: None)
[2025-09-16 22:33:59] [STDOUT] [+0:01:03] 🔎 Verifying download in queue for "Regular Show (2010)" (attempt 3/6)...
[2025-09-16 22:33:59] [STDOUT] [+0:01:03] 
[2025-09-16 22:34:09] [STDERR] [+0:01:13] 2025-09-16 22:34:09,317 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'Regular Show (2010)' (ID: None)
[2025-09-16 22:34:09] [STDOUT] [+0:01:13] 🔎 Verifying download in queue for "Regular Show (2010)" (attempt 4/6)...
[2025-09-16 22:34:09] [STDOUT] [+0:01:13] 
[2025-09-16 22:34:19] [STDERR] [+0:01:23] 2025-09-16 22:34:19,484 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'Regular Show (2010)' (ID: None)
[2025-09-16 22:34:19] [STDOUT] [+0:01:23] 🔎 Verifying download in queue for "Regular Show (2010)" (attempt 5/6)...
[2025-09-16 22:34:19] [STDOUT] [+0:01:23] 
[2025-09-16 22:34:29] [STDERR] [+0:01:33] 2025-09-16 22:34:29,622 - interactive_pipeline_01 - INFO - 🔍 Download not found in SABnzbd: 'Regular Show (2010)' (ID: None)
[2025-09-16 22:34:29] [STDOUT] [+0:01:33] ❌ Download verification failed: "Regular Show (2010)" - submission likely failed
[2025-09-16 22:34:29] [STDOUT] [+0:01:33] 
[2025-09-16 22:34:29] [STDERR] [+0:01:33] 2025-09-16 22:34:29,683 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T22:34:29.683135", "event": "verification_failed", "job_id": "sonarr_364_series", "title": "Regular Show (2010)", "source": "sonarr", "status": "failed", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "sonarr_id": 364, "quality": "Unknown", "error_message": "Download not found in Sonarr queues or history"}
