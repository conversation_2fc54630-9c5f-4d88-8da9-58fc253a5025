=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-16 22:35:14
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_10-35-14-PM.txt
==================================================

[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-16_10-35-14-PM.txt
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 22:35:14
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,429 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,431 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,431 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,432 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,432 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,432 - interactive_pipeline_01 - INFO -    📊 Loaded 8 existing movie records
[2025-09-16 22:35:14] [STDERR] [+0:00:00] 2025-09-16 22:35:14,432 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] ============================================================
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:14] [STDOUT] [+0:00:00]   4. Quit
[2025-09-16 22:35:14] [STDOUT] [+0:00:00] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 🤖 Processing Mode Selection
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] ============================================================
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] How would you like to handle download decisions?
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 📝 Full Auto Mode Details:
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01]    • Automatically selects preflight analysis for every item
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01]    • Automatically chooses max candidates when prompted
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01]    • No manual intervention required - perfect for overnight processing
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:15] [STDOUT] [+0:00:01]    • Falls back gracefully if preflight fails
[2025-09-16 22:35:15] [STDOUT] [+0:00:01] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] ✅ Manual Mode selected - you'll be prompted for each item
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 📁 Loaded 12 tv_shows from C:\Users\<USER>\Videos\PlexAutomator\new_tv_requests.txt
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] ======================================================================
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 📺 TV Shows Available for Processing:
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] ======================================================================
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    1. Ed, Edd n Eddy (1999)                    📚 Complete Series        
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    2. Adventure Time (2010)                    📚 Complete Series        
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    3. The Saddle Club (2003)                   📚 Complete Series        
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    4. Samurai Jack (2001) S01                  📀 Season S01             
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    5. Steven Universe (2013) S02               📀 Season S02             
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    6. Futurama (1999) S01E01                   📺 Episode S01E01         
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    7. The Powerpuff Girls (1998)               📚 Complete Series        
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]       📋 Will use TVDB for chronological episode tracking
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    8. Regular Show (2010) S08E31               📺 Episode S08E31         
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    9. Dexter's Laboratory (1996) S01E01, S01E05, S01E12 📺 Multi-Episodes S01E01, S01E05, S01E12
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   10. Teen Titans (2003) S02E03, S02E07, S02E13 📺 Multi-Episodes S02E03, S02E07, S02E13
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   11. Johnny Bravo (1997) S01E01, S03E15       📺 Multi-Episodes S01E01, S03E15
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   12. Ben 10 (2005) S01E01, S02E13, S04E21     📺 Multi-Episodes S01E01, S02E13, S04E21
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 📊 Legend:
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    📺 Episode    - Single episode download
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    📀 Season     - Full season download (all episodes)
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]    📚 Series     - Complete series (all seasons)
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 📝 Selection Options:
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   • Single: Enter number (e.g., '3')
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   • All: Enter 'all' or 'a'
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   • None: Enter 'none' or 'n' to skip
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:18] [STDOUT] [+0:00:04]   • Quit: Enter 'quit' or 'q'
[2025-09-16 22:35:18] [STDOUT] [+0:00:04] 
[2025-09-16 22:35:28] [STDOUT] [+0:00:13] 
[2025-09-16 22:35:28] [STDOUT] [+0:00:13] ✅ Selected 1 TV shows:
[2025-09-16 22:35:28] [STDOUT] [+0:00:13] 
[2025-09-16 22:35:28] [STDOUT] [+0:00:13]     1. 📚 Futurama (1999) S01E01
[2025-09-16 22:35:28] [STDOUT] [+0:00:13] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 📺 Processing 1 selected TV shows...
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] ============================================================
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDERR] [+0:00:15] 2025-09-16 22:35:29,918 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 📍 Progress: 1/1
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 📺 Processing: Futurama (1999) S01E01
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15]    🎯 Request Type: Specific Episodes
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDOUT] [+0:00:15]    📺 Target: Season 1, Episode 1
[2025-09-16 22:35:29] [STDOUT] [+0:00:15] 
[2025-09-16 22:35:29] [STDERR] [+0:00:15] 2025-09-16 22:35:29,919 - interactive_pipeline_01 - INFO - Processing TV show: Futurama (1999) S01E01 (specific_episodes)
[2025-09-16 22:35:30] [STDERR] [+0:00:15] 2025-09-16 22:35:30,075 - _internal.src.metadata_fetcher - INFO - TMDb TV search with year 1999: 1 results
[2025-09-16 22:35:30] [STDERR] [+0:00:16] 2025-09-16 22:35:30,599 - _internal.utils.fuzzy_matching - INFO - Found 1 exact matches for 'Futurama', prioritizing them
[2025-09-16 22:35:31] [STDERR] [+0:00:17] 2025-09-16 22:35:31,643 - _internal.src.metadata_fetcher - ERROR - Error during TV fuzzy matching for 'Futurama': EnhancedFuzzyMatchingConfig.get_year_tolerance() missing 1 required positional argument: 'content_type'
[2025-09-16 22:35:31] [STDOUT] [+0:00:17] ✅ Found metadata: Futurama (1999)
[2025-09-16 22:35:31] [STDOUT] [+0:00:17] 
[2025-09-16 22:35:31] [STDERR] [+0:00:17] 2025-09-16 22:35:31,686 - interactive_pipeline_01 - INFO - Successfully found TV metadata for: Futurama
[2025-09-16 22:35:31] [STDERR] [+0:00:17] 2025-09-16 22:35:31,688 - interactive_pipeline_01 - INFO - 🧪 Skipping creation of season pack blocking profile (preflight handles pack decisions)
[2025-09-16 22:35:31] [STDERR] [+0:00:17] 2025-09-16 22:35:31,689 - interactive_pipeline_01 - INFO - 📺 TV Quality Strategy (ADAPTIVE): Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-16 22:35:31] [STDERR] [+0:00:17] 2025-09-16 22:35:31,689 - interactive_pipeline_01 - INFO - 📺 Year-based preference hint: 1999 (used for internal logic, not restrictions)
[2025-09-16 22:35:31] [STDERR] [+0:00:17] 2025-09-16 22:35:31,689 - interactive_pipeline_01 - INFO - Searching Sonarr for: Futurama 1999
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,774 - interactive_pipeline_01 - INFO - Selected TV show: Futurama (1999) | tvdbId=73871 | alternatives: [{'title': 'Family Feud (1999)', 'year': 1999, 'tvdbId': 335567, 'score': 378}, {'title': 'Kaj og Andrea (1999)', 'year': 1999, 'tvdbId': 263611, 'score': 330}, {'title': 'Bad Girls (1999)', 'year': 1999, 'tvdbId': 75328, 'score': 327}]
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,777 - interactive_pipeline_01 - INFO - 📁 Matched existing Sonarr root folder: E:\
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,777 - interactive_pipeline_01 - INFO - 📋 Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,777 - interactive_pipeline_01 - INFO - 📺 Adding TV show to Sonarr with 1 quality profile(s): Futurama
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,779 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 6...
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,817 - interactive_pipeline_01 - INFO -    ✅ Added: Futurama (Series ID 365, Profile 6)
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,817 - interactive_pipeline_01 - INFO -    📺 Configuring monitoring for 1 specific episodes
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,817 - interactive_pipeline_01 - INFO -    🔄 Attempt 1/15: Fetching episodes for series 365
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,819 - interactive_pipeline_01 - INFO -    📊 Found 0 episodes in Sonarr
[2025-09-16 22:35:32] [STDERR] [+0:00:18] 2025-09-16 22:35:32,819 - interactive_pipeline_01 - INFO -    ⏳ Episodes not yet populated, waiting 4s (attempt 1/15)
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,822 - interactive_pipeline_01 - INFO -    🔄 Attempt 2/15: Fetching episodes for series 365
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,834 - interactive_pipeline_01 - INFO -    📊 Found 160 episodes in Sonarr
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,835 - interactive_pipeline_01 - INFO -    📋 Found 160 episodes in series
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,835 - interactive_pipeline_01 - INFO -    🎯 Target episodes: {(1, 1)}
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,835 - interactive_pipeline_01 - INFO -       ✓ Will monitor S01E01
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,835 - interactive_pipeline_01 - INFO -    📝 Configuring monitoring for 1 specific episodes
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,838 - interactive_pipeline_01 - WARNING - Failed to update series monitoring via client
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,846 - interactive_pipeline_01 - INFO -    ✅ Successfully configured monitoring for 1 episodes
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,846 - interactive_pipeline_01 - INFO -    ✅ Episode monitoring configuration complete
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,847 - interactive_pipeline_01 - INFO -    ⏭️ Skipping immediate search triggers for 1 episodes (preflight will handle searches)
[2025-09-16 22:35:36] [STDERR] [+0:00:22] 2025-09-16 22:35:36,847 - interactive_pipeline_01 - INFO - 📏 Max episode size threshold configured: 40.0 GB
[2025-09-16 22:35:41] [STDERR] [+0:00:27] 2025-09-16 22:35:41,853 - interactive_pipeline_01 - INFO - Episode size enforcement: no oversized items detected
[2025-09-16 22:35:41] [STDERR] [+0:00:27] 2025-09-16 22:35:41,853 - interactive_pipeline_01 - INFO - ✅ Season pack evaluation handled by preflight analyzer
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 📥 Queued "Futurama (1999)" for download...
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDERR] [+0:00:27] 2025-09-16 22:35:41,854 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T22:35:41.854450", "event": "download_queued", "job_id": "sonarr_365_series", "title": "Futurama (1999)", "source": "sonarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "sonarr_id": 365, "quality": "Unknown"}
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 📊 TV show queued for download: Futurama (1999)
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27]    🔬 Real-time tracking: sonarr_3...
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27]    📺 Series-wide tracking enabled
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDERR] [+0:00:27] 2025-09-16 22:35:41,854 - interactive_pipeline_01 - INFO - Telemetry job started: sonarr_365_series for series 365
[2025-09-16 22:35:41] [STDOUT] [+0:00:27]    📺 Configured for: S01E01 only
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 🔍 Alternative candidate matches (top scoring):
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27]    • Family Feud (1999) (1999) tvdb:335567 score:378
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27]    • Kaj og Andrea (1999) (1999) tvdb:263611 score:330
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27]    • Bad Girls (1999) (1999) tvdb:75328 score:327
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDERR] [+0:00:27] 2025-09-16 22:35:41,855 - interactive_pipeline_01 - INFO - ✅ Successfully added TV show to Sonarr: Futurama
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 🤔 Download Strategy Choice for: Futurama
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] Choose how you want to handle downloads for this show:
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 1. 🔬 Preflight Analysis - Carefully analyze releases before downloading (recommended)
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 2. ⚡ Sonarr Auto-Grab - Let Sonarr immediately search and grab based on quality profiles
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 3. ⏭️  Skip - Add to Sonarr but don't start any downloads yet
[2025-09-16 22:35:41] [STDOUT] [+0:00:27] 
[2025-09-16 22:35:43] [STDOUT] [+0:00:29] 🔬 Using Preflight Analysis for Futurama
[2025-09-16 22:35:43] [STDOUT] [+0:00:29] 
[2025-09-16 22:35:43] [STDERR] [+0:00:29] 2025-09-16 22:35:43,704 - interactive_pipeline_01 - INFO - User selected preflight analysis for: Futurama
[2025-09-16 22:35:43] [STDOUT] [+0:00:29] 🔎 Preflight analyzing 1 season(s): [1]
[2025-09-16 22:35:43] [STDOUT] [+0:00:29] 
[2025-09-16 22:35:43] [STDOUT] [+0:00:29]    Season 1: Episodes [1]
[2025-09-16 22:35:43] [STDOUT] [+0:00:29] 
[2025-09-16 22:35:51] [STDOUT] [+0:00:37] 
[2025-09-16 22:35:51] [STDOUT] [+0:00:37] 🎯 Analyzing Season 1...
[2025-09-16 22:35:51] [STDOUT] [+0:00:37] 
[2025-09-16 22:35:51] [STDOUT] [+0:00:37] 🆕 No existing decision found for Season 1, running fresh analysis
[2025-09-16 22:35:51] [STDOUT] [+0:00:37] 
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,750 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎬 Starting TV preflight analysis - Series: 365, Episodes: 1, Mode: standard
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,750 - preflight_analyzer.tv_show_preflight_selector - INFO - 📋 Analysis configuration - Cache: True, Deduplicate: True, Fresh checks: False
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,750 - preflight_analyzer.cache_observability - INFO - Initialized enhanced cache metrics with max_events=10000
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,751 - preflight_analyzer.memory_cache - INFO - Initialized memory cache with maxsize=1000, ttl=43200s
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,784 - preflight_analyzer.persistent_cache - INFO - Initialized persistent cache at workspace\preflight_cache\cache\analysis_cache.db
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,785 - preflight_analyzer.guid_reconciler - INFO - Initialized GUID reconciler with size_tolerance=0.05, title_threshold=0.8, min_confidence=0.7, groups=True, indexers=True
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,785 - preflight_analyzer.ttl_coordinator - INFO - Initialized TTL coordinator with default policies
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,785 - preflight_analyzer.multi_layer_cache - INFO - Initialized multi-layer cache at workspace\preflight_cache\cache
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,785 - preflight_analyzer.cache - INFO - Initialized decision cache at workspace\preflight_cache\cache
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,809 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 1 episodes from Futurama Season 1
[2025-09-16 22:35:51] [STDERR] [+0:00:37] 2025-09-16 22:35:51,809 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 1 episodes in parallel (max 6 concurrent)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,681 - preflight_analyzer.multi_layer_cache - INFO - L2 content key reconciliation: be276fe2-d61e-4858-9313-4cfa24647e8e -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,681 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: be276fe2..., 8.4ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,681 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: be276fe2..., 8.4ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,681 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama - S01E01 - Space Pilot 3000 - WEBDL-720p → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,683 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 89a18b04-d8df-4fae-b387-bb6949b0917f -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,683 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 89a18b04..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,683 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 89a18b04..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,683 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama - S01E01 - Space Pilot 3000 - WEBDL-720p → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,685 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b721b110-1f53-4a96-a8ac-9a8f0f93ad20 -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,685 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b721b110..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,685 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b721b110..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,686 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama.S01E01.Space.Pilot.3000.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,687 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2882feed-f644-4a3f-9d1e-b9e9db51c6e4 -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,688 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2882feed..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,688 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2882feed..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,688 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama.1999.S01E01.Zeit.und.Raum.3000.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,690 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3b05bebb-5dbb-4dff-80c1-1a3cce059973 -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,690 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3b05bebb..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,690 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3b05bebb..., 1.9ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,690 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama.S01E01.Space.Pilot.3000.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,692 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2498bd03-4af7-42ae-ad3a-3e1516c02050 -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,692 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2498bd03..., 1.8ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,692 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2498bd03..., 1.8ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,692 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,694 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9a8609a2-c17b-4160-8780-24e025b3e269 -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,694 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9a8609a2..., 1.8ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,694 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9a8609a2..., 1.8ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,694 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 91cd5e8f-b86a-4d87-aa96-eff7b8b951da -> movie:unknown
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 91cd5e8f..., 0.8ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 91cd5e8f..., 0.8ms)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 22:35:52 Cache hit: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.0006)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 Episode coverage: 1/1 episodes (100.0%)
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 100% episode coverage - using individual episodes only
[2025-09-16 22:35:52] [STDERR] [+0:00:38] 2025-09-16 22:35:52,695 - preflight_analyzer.tv_show_preflight_selector - INFO - 📝 Final plan: Using 1 individual episodes
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    📊 Episode queued: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD (tracking enabled)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,358 - preflight_analyzer.tv_show_preflight_selector - INFO -    📞 Callback executed for episode: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,358 - preflight_analyzer.tv_show_preflight_selector - INFO - 💾 Cache performance: 8/8 hits (100.0%) - saved significant analysis time!
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,359 - preflight_analyzer.tv_show_preflight_selector - INFO - ✅ TV preflight analysis completed in 2.61s - Strategy: episodes
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    🔧 DEBUG: Selecting best from 8 candidates for episode 60842
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    🔧 NEW LOGIC: Selected Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.W... - 1080p, 0.94GB, 🇺🇸, Yassmiso, risk: 0.0006
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    🐛 DEBUG: Filtering 0 accepted packs for season 1
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    🐛 DEBUG: Found 0 season-specific packs
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    🐛 DEBUG: No season-specific packs found, season_pack = None
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    ➤ Season 1: 1/1 episodes covered (100.00%) with 8 files, strategy=episodes
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    📋 Season 1: 1 downloads handled by immediate callback
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📊 Combined Results: 1 total episodes analyzed, 1 acceptable episodes + 0 season packs found
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📊 Queue Status: 1 items queued across 1 season(s) (telemetry tracking enabled)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📝 Season 1 decision saved: workspace\preflight_decisions\tv_shows\Futurama_S01.json
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] ✅ Preflight found and started downloads for 1 episodes + 0 packs across 1 season(s)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 🔬 Preflight Episode Selections (already downloading):
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    #1. 📺 Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]        💾 Size: 0.94 GB (1,007,222,772 bytes)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]        ⚡ Risk: 0.0006 | Missing: 0.0% | Decision: ACCEPT
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📊 Preflight Summary: 1 episodes + 0 packs | Total: 0.94 GB
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 🎯 Downloads started immediately after each season analysis
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    (No waiting for all analysis to complete - optimal efficiency!)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,361 - interactive_pipeline_01 - WARNING - Preflight skipped: missing Sonarr series id
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,366 - interactive_pipeline_01 - INFO - Stored enhanced TV metadata for: Futurama (1999)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] ============================================================
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📊 TV Download Summary (compact)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] ============================================================
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] • Futurama (1999) S01E01 → Unknown
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] ============================================================
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📊 Processing Complete!
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] ============================================================
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 🎬 Movies processed: 0
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📺 TV shows processed: 1
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 📊 Total content processed: 1
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 🔍 Verifying 1 downloads actually started...
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:39]    This replaces guesswork with real verification!
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,367 - interactive_pipeline_01 - INFO - 🔄 Starting real-time download monitoring (interval: 5s)
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,368 - interactive_pipeline_01 - INFO - 📦 SABnzbd match found: 'Futurama (1999)' -> 'futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd' (similarity: 1.00)
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] ✅ Download verified: "Futurama (1999)" is now downloading
[2025-09-16 22:35:54] [STDOUT] [+0:00:39] 
[2025-09-16 22:35:54] [STDERR] [+0:00:39] 2025-09-16 22:35:54,368 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-16T22:35:54.368849", "event": "download_verified", "job_id": "sonarr_365_series", "title": "Futurama (1999)", "source": "sonarr", "status": "downloading", "progress": 0.0, "size_total": 1038258012, "size_downloaded": 0, "speed_bps": 0.0, "eta": "0:00:00", "sonarr_id": 365, "sab_nzo_id": "SABnzbd_nzo_usrno3_i", "quality": "Unknown"}
[2025-09-16 22:35:54] [STDERR] [+0:00:40] 2025-09-16 22:35:54,495 - interactive_pipeline_01 - INFO - 🔧 Intelligent Fallback System initialized
[2025-09-16 22:35:54] [STDERR] [+0:00:40] 2025-09-16 22:35:54,495 - interactive_pipeline_01 - INFO -    📁 Preflight decisions: workspace\preflight_decisions
[2025-09-16 22:35:54] [STDERR] [+0:00:40] 2025-09-16 22:35:54,495 - interactive_pipeline_01 - INFO -    🎬 Radarr URL: http://localhost:7878
[2025-09-16 22:35:54] [STDERR] [+0:00:40] 2025-09-16 22:35:54,495 - interactive_pipeline_01 - INFO -    🔑 API Key configured: ✅
[2025-09-16 22:35:54] [STDERR] [+0:00:40] 2025-09-16 22:35:54,495 - interactive_pipeline_01 - INFO -    📊 Telemetry integration: ✅
[2025-09-16 22:35:54] [STDERR] [+0:00:40] 2025-09-16 22:35:54,496 - interactive_pipeline_01 - INFO - 🛡️ Intelligent fallback system initialized with telemetry integration
[2025-09-16 22:35:54] [STDOUT] [+0:00:40] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:40] 📊 Real-time telemetry monitoring started
[2025-09-16 22:35:54] [STDOUT] [+0:00:40] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:40] 📄 Telemetry dashboard log: logs\telemetry_dashboard_2025-09-16_10-35-54-PM.txt
[2025-09-16 22:35:54] [STDOUT] [+0:00:40] 
[2025-09-16 22:35:54] [STDOUT] [+0:00:40]    (Dashboard output will be written to separate file to keep main log clean)
[2025-09-16 22:35:54] [STDOUT] [+0:00:40] 
[2025-09-16 22:36:14] [STDERR] [+0:01:00] 2025-09-16 22:36:14,666 - interactive_pipeline_01 - INFO - 🔄 Triggering post-processing for completed download: Futurama (1999)
[2025-09-16 22:36:14] [STDERR] [+0:01:00] 2025-09-16 22:36:14,670 - interactive_pipeline_01 - INFO - ✅ Post-processing triggered for: Futurama (1999) (PID: 81932)
