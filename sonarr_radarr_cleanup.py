#!/usr/bin/env python3
"""
Enhanced Sonarr/Radarr Cleanup Script

This version focuses on the operations that actually work with most Sonarr/Radarr installations
and provides clear feedback about what's possible vs what isn't.
"""

import json
import logging
import urllib.parse
import urllib.request
import urllib.error
from pathlib import Path
from typing import Any, Dict, Optional
import time
import sys
import configparser


class EnhancedSonarrRadarrCleaner:
    """Enhanced cleaner with better error handling and realistic expectations"""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize with configuration"""
        self.config_path = config_path or self._find_config_path()
        self.config = self._load_config()
        self.logger = self._setup_logging()

        # Extract connection details from INI sections
        self.sonarr_config = self._get_sonarr_config()
        self.radarr_config = self._get_radarr_config()
        
    def _find_config_path(self) -> str:
        """Find the settings.ini config file"""
        script_dir = Path(__file__).parent
        config_paths = [
            script_dir / "_internal" / "config" / "settings.ini",
            script_dir / "config" / "settings.ini"
        ]

        for path in config_paths:
            if path.exists():
                return str(path)

        raise FileNotFoundError("Could not find settings.ini")

    def _load_config(self) -> configparser.ConfigParser:
        """Load configuration from INI file"""
        try:
            config = configparser.ConfigParser()
            config.read(self.config_path)
            return config
        except Exception as e:
            raise Exception(f"Failed to load config from {self.config_path}: {e}")

    def _get_sonarr_config(self) -> Dict[str, Any]:
        """Extract Sonarr configuration from INI format"""
        if not self.config.has_section('Sonarr'):
            return {'enabled': False}

        return {
            'enabled': True,
            'connection_url': self.config.get('Sonarr', 'url', fallback='http://localhost:8989'),
            'api_key': self.config.get('Sonarr', 'api_key', fallback='')
        }

    def _get_radarr_config(self) -> Dict[str, Any]:
        """Extract Radarr configuration from INI format"""
        if not self.config.has_section('Radarr'):
            return {'enabled': False}

        return {
            'enabled': True,
            'connection_url': self.config.get('Radarr', 'url', fallback='http://localhost:7878'),
            'api_key': self.config.get('Radarr', 'api_key', fallback='')
        }

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the cleanup operations"""
        logger = logging.getLogger('enhanced_cleanup')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _api_request(self, base_url: str, api_key: str, path: str, method: str = 'GET', 
                     params: Optional[Dict[str, Any]] = None, 
                     payload: Optional[Dict[str, Any]] = None,
                     timeout: float = 30.0) -> Any:
        """Make API request to Sonarr/Radarr"""
        try:
            # Build URL
            q = urllib.parse.urlencode(params or {})
            url = f"{base_url.rstrip('/')}{path}{'?' + q if q else ''}"
            
            # Setup request
            headers = {
                'X-Api-Key': api_key,
                'User-Agent': 'PlexAutomator-Enhanced-Cleanup/1.0',
                'Accept': 'application/json'
            }
            
            if method in ['POST', 'PUT', 'DELETE'] and payload is not None:
                headers['Content-Type'] = 'application/json'
                data = json.dumps(payload).encode('utf-8')
            else:
                data = None
            
            req = urllib.request.Request(url, data=data, headers=headers, method=method)
            
            with urllib.request.urlopen(req, timeout=timeout) as resp:
                if resp.status == 204:  # No content
                    return None
                response_text = resp.read().decode('utf-8', errors='replace')
                if response_text:
                    return json.loads(response_text)
                return None
                
        except urllib.error.HTTPError as e:
            if e.code == 404:
                return None
            else:
                error_msg = f"HTTP Error {e.code} for {method} {path}"
                try:
                    error_body = e.read().decode('utf-8')
                    error_msg += f": {error_body}"
                except:
                    pass
                raise Exception(error_msg)
        except Exception as e:
            raise Exception(f"API request failed for {method} {path}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of both services"""
        status = {
            'sonarr': {'available': False, 'series': 0, 'history': 0, 'queue': 0},
            'radarr': {'available': False, 'movies': 0, 'history': 0, 'queue': 0}
        }
        
        # Check Sonarr
        if self.sonarr_config.get('enabled', False):
            try:
                base_url = self.sonarr_config['connection_url']
                api_key = self.sonarr_config['api_key']
                
                # Test connection
                system = self._api_request(base_url, api_key, "/api/v3/system/status")
                if system:
                    status['sonarr']['available'] = True
                    status['sonarr']['version'] = system.get('version', 'unknown')
                    
                    # Get counts
                    series = self._api_request(base_url, api_key, "/api/v3/series")
                    status['sonarr']['series'] = len(series) if series else 0
                    
                    history = self._api_request(base_url, api_key, "/api/v3/history", 
                                              params={'pageSize': 1})
                    status['sonarr']['history'] = history.get('totalRecords', 0) if history else 0
                    
                    queue = self._api_request(base_url, api_key, "/api/v3/queue")
                    status['sonarr']['queue'] = len(queue.get('records', [])) if queue else 0
                    
            except Exception as e:
                self.logger.warning(f"Could not get Sonarr status: {e}")
        
        # Check Radarr
        if self.radarr_config.get('enabled', False):
            try:
                base_url = self.radarr_config['connection_url']
                api_key = self.radarr_config['api_key']
                
                # Test connection
                system = self._api_request(base_url, api_key, "/api/v3/system/status")
                if system:
                    status['radarr']['available'] = True
                    status['radarr']['version'] = system.get('version', 'unknown')
                    
                    # Get counts
                    movies = self._api_request(base_url, api_key, "/api/v3/movie")
                    status['radarr']['movies'] = len(movies) if movies else 0
                    
                    history = self._api_request(base_url, api_key, "/api/v3/history", 
                                              params={'pageSize': 1})
                    status['radarr']['history'] = history.get('totalRecords', 0) if history else 0
                    
                    queue = self._api_request(base_url, api_key, "/api/v3/queue")
                    status['radarr']['queue'] = len(queue.get('records', [])) if queue else 0
                    
            except Exception as e:
                self.logger.warning(f"Could not get Radarr status: {e}")
        
        return status
    
    def clear_sonarr_history(self) -> bool:
        """Clear all Sonarr activity history"""
        if not self.sonarr_config.get('enabled', False):
            self.logger.warning("Sonarr is not enabled in configuration")
            return False
            
        try:
            base_url = self.sonarr_config['connection_url']
            api_key = self.sonarr_config['api_key']
            
            self.logger.info("🔄 Clearing Sonarr history...")
            
            # Get all history records
            history_records = self._api_request(base_url, api_key, "/api/v3/history", 
                                              params={'pageSize': 10000})
            
            if not history_records or not history_records.get('records'):
                self.logger.info("No Sonarr history records found")
                return True
            
            records = history_records['records']
            self.logger.info(f"Found {len(records)} history records to delete")
            
            success_count = 0
            for record in records:
                record_id = record.get('id')
                if record_id:
                    try:
                        self._api_request(base_url, api_key, f"/api/v3/history/{record_id}", 
                                        method='DELETE')
                        success_count += 1
                    except Exception as e:
                        self.logger.warning(f"Failed to delete history record {record_id}: {e}")
                        
            self.logger.info(f"✅ Successfully deleted {success_count}/{len(records)} Sonarr history records")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"❌ Failed to clear Sonarr history: {e}")
            return False
    
    def clear_radarr_history(self) -> bool:
        """Clear all Radarr activity history"""
        if not self.radarr_config.get('enabled', False):
            self.logger.warning("Radarr is not enabled in configuration")
            return False
            
        try:
            base_url = self.radarr_config['connection_url']
            api_key = self.radarr_config['api_key']
            
            self.logger.info("🔄 Clearing Radarr history...")
            
            # Get all history records
            history_records = self._api_request(base_url, api_key, "/api/v3/history", 
                                              params={'pageSize': 10000})
            
            if not history_records or not history_records.get('records'):
                self.logger.info("No Radarr history records found")
                return True
            
            records = history_records['records']
            self.logger.info(f"Found {len(records)} history records to delete")
            
            success_count = 0
            for record in records:
                record_id = record.get('id')
                if record_id:
                    try:
                        self._api_request(base_url, api_key, f"/api/v3/history/{record_id}", 
                                        method='DELETE')
                        success_count += 1
                    except Exception as e:
                        self.logger.warning(f"Failed to delete history record {record_id}: {e}")
                        
            self.logger.info(f"✅ Successfully deleted {success_count}/{len(records)} Radarr history records")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"❌ Failed to clear Radarr history: {e}")
            return False

    def clear_download_queue(self) -> bool:
        """Clear download queue from both services"""
        success = True
        
        # Clear Sonarr queue
        if self.sonarr_config.get('enabled', False):
            try:
                base_url = self.sonarr_config['connection_url']
                api_key = self.sonarr_config['api_key']
                
                self.logger.info("🔄 Clearing Sonarr download queue...")
                
                # Clear active queue
                queue = self._api_request(base_url, api_key, "/api/v3/queue")
                removed_count = 0
                
                if queue and 'records' in queue and len(queue['records']) > 0:
                    for record in queue['records']:
                        try:
                            self._api_request(base_url, api_key, f"/api/v3/queue/{record['id']}", 
                                            method='DELETE', params={'removeFromClient': 'true'})
                            removed_count += 1
                        except Exception as e:
                            self.logger.warning(f"Failed to remove active queue item {record['id']}: {e}")
                    
                    self.logger.info(f"✅ Removed {removed_count} active items from Sonarr queue")
                else:
                    self.logger.info("✅ Sonarr active queue is empty")
                
                # Clear completed/stuck items from queue details
                queue_details = self._api_request(base_url, api_key, "/api/v3/queue/details")
                details_removed_count = 0
                
                if queue_details and isinstance(queue_details, list) and len(queue_details) > 0:
                    self.logger.info(f"Found {len(queue_details)} completed/stuck items in queue details")
                    for record in queue_details:
                        try:
                            record_id = record.get('id')
                            if record_id:
                                self._api_request(base_url, api_key, f"/api/v3/queue/{record_id}", 
                                                method='DELETE', params={'removeFromClient': 'true'})
                                details_removed_count += 1
                        except Exception as e:
                            record_title = record.get('title', 'Unknown')
                            self.logger.warning(f"Failed to remove completed queue item '{record_title}': {e}")
                    
                    if details_removed_count > 0:
                        self.logger.info(f"✅ Removed {details_removed_count} completed/stuck items from Sonarr queue")
                    else:
                        self.logger.warning("⚠️ Failed to remove any completed/stuck items")
                elif queue_details and isinstance(queue_details, dict) and queue_details.get('records'):
                    # Handle paginated response format
                    records = queue_details['records']
                    if len(records) > 0:
                        self.logger.info(f"Found {len(records)} completed/stuck items in queue details")
                        for record in records:
                            try:
                                record_id = record.get('id')
                                if record_id:
                                    self._api_request(base_url, api_key, f"/api/v3/queue/{record_id}", 
                                                    method='DELETE', params={'removeFromClient': 'true'})
                                    details_removed_count += 1
                            except Exception as e:
                                record_title = record.get('title', 'Unknown')
                                self.logger.warning(f"Failed to remove completed queue item '{record_title}': {e}")
                        
                        if details_removed_count > 0:
                            self.logger.info(f"✅ Removed {details_removed_count} completed/stuck items from Sonarr queue")
                        else:
                            self.logger.warning("⚠️ Failed to remove any completed/stuck items")
                    else:
                        self.logger.info("✅ No completed/stuck items in Sonarr queue details")
                else:
                    self.logger.info("✅ No completed/stuck items in Sonarr queue details")
                
                total_removed = removed_count + details_removed_count
                if total_removed > 0:
                    self.logger.info(f"🎉 Total: Removed {total_removed} items from Sonarr queue")
                else:
                    self.logger.info("✅ Sonarr queue was already empty")
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to clear Sonarr queue: {e}")
                success = False
        
        # Clear Radarr queue
        if self.radarr_config.get('enabled', False):
            try:
                base_url = self.radarr_config['connection_url']
                api_key = self.radarr_config['api_key']
                
                self.logger.info("🔄 Clearing Radarr download queue...")
                queue = self._api_request(base_url, api_key, "/api/v3/queue")
                
                if queue and 'records' in queue:
                    removed_count = 0
                    for record in queue['records']:
                        try:
                            self._api_request(base_url, api_key, f"/api/v3/queue/{record['id']}", 
                                            method='DELETE', params={'removeFromClient': 'true'})
                            removed_count += 1
                        except Exception as e:
                            self.logger.warning(f"Failed to remove queue item {record['id']}: {e}")
                    
                    if removed_count > 0:
                        self.logger.info(f"✅ Removed {removed_count} items from Radarr queue")
                    else:
                        self.logger.info("✅ Radarr queue was already empty")
                else:
                    self.logger.info("✅ Radarr queue is empty")
                    
            except Exception as e:
                self.logger.error(f"❌ Failed to clear Radarr queue: {e}")
                success = False
        
        return success
    
    def remove_all_sonarr_series(self, delete_files: bool = False) -> bool:
        """Remove all series from Sonarr"""
        if not self.sonarr_config.get('enabled', False):
            self.logger.info("Sonarr is disabled in config, skipping...")
            return True
            
        base_url = self.sonarr_config['connection_url']
        api_key = self.sonarr_config['api_key']
        
        try:
            self.logger.info("🗑️ Removing all series from Sonarr...")
            
            # Get all series
            series_list = self._api_request(base_url, api_key, "/api/v3/series")
            
            if series_list:
                self.logger.info(f"Found {len(series_list)} series to remove")
                
                if not delete_files:
                    self.logger.info("📁 Media files will be preserved")
                else:
                    self.logger.warning("⚠️ Media files will be DELETED!")
                
                removed_count = 0
                for i, series in enumerate(series_list):
                    series_id = None
                    try:
                        series_id = series['id']
                        series_title = series.get('title', f'Series {series_id}')
                        
                        # Delete series with optional file deletion
                        params = {'deleteFiles': 'true' if delete_files else 'false'}
                        self._api_request(base_url, api_key, f"/api/v3/series/{series_id}", 
                                        method='DELETE', params=params)
                        
                        self.logger.info(f"Deleted series: {series_title}")
                        removed_count += 1
                        
                        if (i + 1) % 5 == 0:
                            self.logger.info(f"Progress: {i + 1}/{len(series_list)} series removed")
                            time.sleep(1)  # Rate limiting
                            
                    except Exception as e:
                        series_ref = series.get('title', series_id) if series_id else 'Unknown'
                        self.logger.warning(f"Failed to delete series {series_ref}: {e}")
                
                if removed_count > 0:
                    self.logger.info(f"✅ Successfully removed {removed_count} series from Sonarr")
                else:
                    self.logger.warning("⚠️ No series were removed")
            else:
                self.logger.info("No series found in Sonarr")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to remove Sonarr series: {e}")
            return False
    
    def remove_all_radarr_movies(self, delete_files: bool = False) -> bool:
        """Remove all movies from Radarr"""
        if not self.radarr_config.get('enabled', False):
            self.logger.info("Radarr is disabled in config, skipping...")
            return True
            
        base_url = self.radarr_config['connection_url']
        api_key = self.radarr_config['api_key']
        
        try:
            self.logger.info("🗑️ Removing all movies from Radarr...")
            
            # Get all movies
            movies_list = self._api_request(base_url, api_key, "/api/v3/movie")
            
            if movies_list:
                self.logger.info(f"Found {len(movies_list)} movies to remove")
                
                if not delete_files:
                    self.logger.info("📁 Media files will be preserved")
                else:
                    self.logger.warning("⚠️ Media files will be DELETED!")
                
                removed_count = 0
                for i, movie in enumerate(movies_list):
                    movie_id = None
                    try:
                        movie_id = movie['id']
                        movie_title = movie.get('title', f'Movie {movie_id}')
                        
                        # Delete movie with optional file deletion
                        params = {'deleteFiles': 'true' if delete_files else 'false'}
                        self._api_request(base_url, api_key, f"/api/v3/movie/{movie_id}", 
                                        method='DELETE', params=params)
                        
                        self.logger.info(f"Deleted movie: {movie_title}")
                        removed_count += 1
                        
                        if (i + 1) % 5 == 0:
                            self.logger.info(f"Progress: {i + 1}/{len(movies_list)} movies removed")
                            time.sleep(1)  # Rate limiting
                            
                    except Exception as e:
                        movie_ref = movie.get('title', movie_id) if movie_id else 'Unknown'
                        self.logger.warning(f"Failed to delete movie {movie_ref}: {e}")
                
                if removed_count > 0:
                    self.logger.info(f"✅ Successfully removed {removed_count} movies from Radarr")
                else:
                    self.logger.warning("⚠️ No movies were removed")
            else:
                self.logger.info("No movies found in Radarr")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to remove Radarr movies: {e}")
            return False
    
    def reset_for_fresh_start(self) -> bool:
        """Reset both services for a fresh start - removes content but preserves files"""
        self.logger.info("🚀 Starting fresh reset (removes content, keeps files)...")
        
        success = True
        
        # Step 1: Clear download queues
        self.logger.info("Step 1: Clearing download queues...")
        if not self.clear_download_queue():
            success = False
        
        # Step 2: Remove all content
        self.logger.info("Step 2: Removing all series and movies...")
        if not self.remove_all_sonarr_series(delete_files=False):
            success = False
        
        if not self.remove_all_radarr_movies(delete_files=False):
            success = False
        
        if success:
            self.logger.info("🎉 Fresh reset completed successfully!")
            self.logger.info("📁 All media files have been preserved")
            self.logger.info("🆕 Sonarr and Radarr are now ready for new content")
        else:
            self.logger.warning("⚠️ Fresh reset completed with some errors")
        
        return success
    
    def clear_all_cache(self) -> bool:
        """
        Clear all cache data across the entire PlexAutomator system.
        
        This removes:
        - Analysis cache databases
        - Decision history files  
        - Metadata cache
        - Python bytecode cache
        - Temporary processing files
        
        Returns:
            bool: True if successful, False otherwise
        """
        import os
        import shutil
        from pathlib import Path
        
        try:
            base_path = Path(__file__).parent
            
            cache_locations = [
                # Main cache databases
                "data/memory_store.db",
                "workspace/preflight_cache/cache/analysis_cache.db",
                "workspace/preflight_cache/cache/analysis_cache.db-shm", 
                "workspace/preflight_cache/cache/analysis_cache.db-wal",
                
                # Legacy cache files
                "_internal/data/release_failure_tracker.db",
                "_internal/data/pipeline_state.db",
                "_internal/data/movie_metadata.db",
                
                # Decision history files
                "data/preflight_history.json",
                
                # Python cache
                "preflight_analyzer/__pycache__",
                "_internal/__pycache__",
                "__pycache__",
            ]
            
            cache_directories = [
                # Cache directories that may contain cached data
                "workspace/preflight_cache/movies",
                "workspace/preflight_cache/tv_shows",
                "workspace/preflight_decisions",
                "data/metadata_cache",
                
                # Temp directories
                "workspace/.temp_contexts",
                "temp",
                ".cache"
            ]
            
            cleared_count = 0
            
            print("🗑️  CLEARING ALL CACHE DATA FOR FRESH ANALYSIS")
            print("=" * 60)
            
            # Clear specific cache files
            for cache_file in cache_locations:
                full_path = base_path / cache_file
                
                if full_path.exists():
                    try:
                        if full_path.is_file():
                            full_path.unlink()
                            print(f"✅ Deleted file: {cache_file}")
                            cleared_count += 1
                        elif full_path.is_dir():
                            shutil.rmtree(full_path)
                            print(f"✅ Deleted directory: {cache_file}")
                            cleared_count += 1
                    except Exception as e:
                        print(f"❌ Failed to delete {cache_file}: {e}")
                        self.logger.error(f"Failed to delete {cache_file}: {e}")
            
            # Clear cache directories (but preserve structure)
            for cache_dir in cache_directories:
                full_path = base_path / cache_dir
                
                if full_path.exists() and full_path.is_dir():
                    try:
                        # Remove all contents but keep the directory
                        for item in full_path.iterdir():
                            if item.is_file():
                                item.unlink()
                                cleared_count += 1
                            elif item.is_dir():
                                shutil.rmtree(item)
                                cleared_count += 1
                        
                        print(f"✅ Cleared directory contents: {cache_dir}")
                    except Exception as e:
                        print(f"❌ Failed to clear {cache_dir}: {e}")
                        self.logger.error(f"Failed to clear {cache_dir}: {e}")
            
            # Clear any DecisionCache SQLite databases
            for db_file in base_path.rglob("*.db"):
                if any(keyword in str(db_file).lower() for keyword in ['cache', 'decision', 'analysis', 'preflight']):
                    try:
                        db_file.unlink()
                        print(f"✅ Deleted cache database: {db_file.relative_to(base_path)}")
                        cleared_count += 1
                    except Exception as e:
                        print(f"❌ Failed to delete {db_file}: {e}")
                        self.logger.error(f"Failed to delete {db_file}: {e}")
            
            # Clear SQLite WAL and SHM files
            for wal_file in base_path.rglob("*.db-wal"):
                try:
                    wal_file.unlink()
                    cleared_count += 1
                except:
                    pass
                    
            for shm_file in base_path.rglob("*.db-shm"):
                try:
                    shm_file.unlink()
                    cleared_count += 1
                except:
                    pass
            
            print("=" * 60)
            print(f"🎯 CACHE CLEARING COMPLETE!")
            print(f"   Total items cleared: {cleared_count}")
            print(f"   System ready for fresh analysis in RELIABILITY mode")
            print(f"   All files will now be analyzed individually!")
            print("=" * 60)
            
            self.logger.info(f"Cache clearing completed successfully - {cleared_count} items cleared")
            return True
            
        except Exception as e:
            print(f"❌ Error during cache clearing: {e}")
            self.logger.error(f"Cache clearing failed: {e}")
            return False
    
    def interactive_menu(self):
        """Interactive menu for cleanup operations"""
        while True:
            print("\n" + "="*60)
            print("🧹 ENHANCED SONARR/RADARR CLEANUP MENU")
            print("="*60)
            
            # Show current status
            status = self.get_status()
            
            if status['sonarr']['available']:
                print(f"📺 Sonarr: {status['sonarr']['series']} series, {status['sonarr']['queue']} in queue")
            else:
                print("📺 Sonarr: Not available")
            
            if status['radarr']['available']:
                print(f"🎬 Radarr: {status['radarr']['movies']} movies, {status['radarr']['queue']} in queue")
            else:
                print("🎬 Radarr: Not available")
            
            print("\nAvailable Operations:")
            print("1. Clear Download Queues Only")
            print("2. Clear History Only (Sonarr + Radarr)")
            print("3. Fresh Reset (Remove content, keep files)")
            print("4. Nuclear Reset (Remove content AND files) ⚠️ DANGEROUS")
            print("5. Remove Sonarr Series Only")
            print("6. Remove Radarr Movies Only") 
            print("7. Show Detailed Status")
            print("8. Clear All Cache Data (Preflight/Analysis)")
            print("9. Back to Main Menu")
            print("="*60)
            
            try:
                choice = input("\nSelect option (1-9): ").strip()
                
                if choice == "1":
                    print("\n🔄 Clearing download queues...")
                    if self.clear_download_queue():
                        print("✅ Download queues cleared successfully!")
                    else:
                        print("❌ Some errors occurred while clearing queues")
                
                elif choice == "2":
                    print("\n🔄 Clearing history from both services...")
                    sonarr_success = self.clear_sonarr_history()
                    radarr_success = self.clear_radarr_history()
                    if sonarr_success and radarr_success:
                        print("✅ History cleared successfully from both services!")
                    elif sonarr_success or radarr_success:
                        print("⚠️ History cleared from some services (check logs for details)")
                    else:
                        print("❌ Failed to clear history from both services")
                
                elif choice == "3":
                    print("\n⚠️ This will remove all series and movies from Sonarr/Radarr")
                    print("📁 Media files will be preserved on disk")
                    confirm = input("Type 'RESET' to confirm: ").strip()
                    if confirm == "RESET":
                        self.reset_for_fresh_start()
                    else:
                        print("❌ Operation cancelled")
                
                elif choice == "4":
                    print("\n🚨 DANGER: This will DELETE ALL MEDIA FILES!")
                    print("This action cannot be undone!")
                    confirm1 = input("Type 'DELETE FILES' to confirm: ").strip()
                    if confirm1 == "DELETE FILES":
                        confirm2 = input("Are you absolutely sure? Type 'YES DELETE EVERYTHING': ").strip()
                        if confirm2 == "YES DELETE EVERYTHING":
                            print("\n💀 Performing nuclear reset...")
                            queue_success = self.clear_download_queue()
                            sonarr_success = self.remove_all_sonarr_series(delete_files=True)
                            radarr_success = self.remove_all_radarr_movies(delete_files=True)
                            
                            if queue_success and sonarr_success and radarr_success:
                                print("✅ Nuclear reset completed successfully!")
                            else:
                                print("❌ Nuclear reset completed with errors")
                        else:
                            print("❌ Operation cancelled")
                    else:
                        print("❌ Operation cancelled")
                
                elif choice == "5":
                    print("\n📺 Removing all Sonarr series...")
                    delete_files = input("Delete media files too? (y/N): ").strip().lower() == 'y'
                    if self.remove_all_sonarr_series(delete_files):
                        print("✅ Sonarr series removed successfully!")
                    else:
                        print("❌ Some errors occurred")
                
                elif choice == "6":
                    print("\n🎬 Removing all Radarr movies...")
                    delete_files = input("Delete media files too? (y/N): ").strip().lower() == 'y'
                    if self.remove_all_radarr_movies(delete_files):
                        print("✅ Radarr movies removed successfully!")
                    else:
                        print("❌ Some errors occurred")
                
                elif choice == "7":
                    print("\n📊 Detailed Status:")
                    print("-" * 40)
                    
                    if status['sonarr']['available']:
                        print(f"📺 Sonarr v{status['sonarr']['version']}:")
                        print(f"   Series: {status['sonarr']['series']}")
                        print(f"   History records: {status['sonarr']['history']}")
                        print(f"   Queue items: {status['sonarr']['queue']}")
                    else:
                        print("📺 Sonarr: Not available or disabled")
                    
                    if status['radarr']['available']:
                        print(f"🎬 Radarr v{status['radarr']['version']}:")
                        print(f"   Movies: {status['radarr']['movies']}")
                        print(f"   History records: {status['radarr']['history']}")
                        print(f"   Queue items: {status['radarr']['queue']}")
                    else:
                        print("🎬 Radarr: Not available or disabled")
                
                elif choice == "8":
                    print("\n🗑️ Clearing all cache data...")
                    print("⚠️ This will clear all preflight analysis cache, decision history, and metadata cache")
                    print("🔄 The system will analyze all files fresh on the next run (reliability mode)")
                    confirm = input("Continue with cache clearing? (y/N): ").strip().lower()
                    if confirm == 'y':
                        if self.clear_all_cache():
                            print("✅ All cache data cleared successfully!")
                            print("🎯 System ready for fresh analysis - all files will be analyzed individually")
                        else:
                            print("❌ Some errors occurred during cache clearing")
                    else:
                        print("❌ Cache clearing cancelled")
                
                elif choice == "9":
                    print("Returning to main menu...")
                    break
                
                else:
                    print("❌ Invalid choice. Please select 1-9.")
                
            except KeyboardInterrupt:
                print("\n\n❌ Operation cancelled by user")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


def main():
    """Main function for standalone execution"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced Sonarr/Radarr Cleanup Tool')
    parser.add_argument('--interactive', action='store_true', help='Interactive menu')
    parser.add_argument('--fresh-reset', action='store_true', help='Fresh reset (remove content, keep files)')
    parser.add_argument('--nuclear', action='store_true', help='Nuclear reset (remove content AND files)')
    parser.add_argument('--queue-only', action='store_true', help='Clear download queue only')
    parser.add_argument('--status', action='store_true', help='Show status only')
    parser.add_argument('--config', help='Path to config file')
    
    args = parser.parse_args()
    
    try:
        cleaner = EnhancedSonarrRadarrCleaner(args.config)
        
        if args.interactive:
            cleaner.interactive_menu()
        elif args.fresh_reset:
            success = cleaner.reset_for_fresh_start()
            sys.exit(0 if success else 1)
        elif args.nuclear:
            print("🚨 NUCLEAR RESET: This will DELETE ALL MEDIA FILES!")
            confirm = input("Type 'DELETE EVERYTHING' to confirm: ").strip()
            if confirm == "DELETE EVERYTHING":
                queue_success = cleaner.clear_download_queue()
                sonarr_success = cleaner.remove_all_sonarr_series(delete_files=True)
                radarr_success = cleaner.remove_all_radarr_movies(delete_files=True)
                success = queue_success and sonarr_success and radarr_success
                sys.exit(0 if success else 1)
            else:
                print("❌ Operation cancelled")
                sys.exit(1)
        elif args.queue_only:
            success = cleaner.clear_download_queue()
            sys.exit(0 if success else 1)
        elif args.status:
            status = cleaner.get_status()
            print("\n📊 Current Status:")
            if status['sonarr']['available']:
                print(f"📺 Sonarr: {status['sonarr']['series']} series, {status['sonarr']['queue']} in queue")
            else:
                print("📺 Sonarr: Not available")
            
            if status['radarr']['available']:
                print(f"🎬 Radarr: {status['radarr']['movies']} movies, {status['radarr']['queue']} in queue")
            else:
                print("🎬 Radarr: Not available")
        else:
            # Default to interactive
            cleaner.interactive_menu()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
