from __future__ import annotations
"""
Integrated preflight selection utilities for embedding inside pipeline stage 01.

DEPRECATED: This module is being replaced by the unified tv_show_preflight_selector.py.
The functions in this module now act as compatibility wrappers around the new unified system.

Legacy Responsibilities (now delegated to tv_show_preflight_selector.py):
- Given a Sonarr series_id + target season, gather episode IDs.
- Run episode-level preflight analysis.
- If fewer than threshold fraction are acceptable, attempt a season pack search and analysis.
- Return a structured decision without performing grabs (caller may decide).

This keeps CLI concerns out of pipeline code and allows reuse + easier unit testing.
"""
import asyncio
import json
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Sequence
from .shared_logic import BasicRelease, rank_releases
from .history_store import DecisionHistory
from pathlib import Path
import urllib.request
import urllib.parse

from .indexer_client import extract_newznab_id, fetch_nzb


def _extract_additional_episodes_from_title(title: str, target_eps: List[Dict[str, Any]]) -> set[int]:
    """
    Extract additional episode IDs from multi-episode file titles.
    
    Args:
        title: Release title like "Adventure.Time.S09E02-E03.720p.HDTV.x264-W4F"
        target_eps: List of episode objects with 'id' and 'episodeNumber' fields
        
    Returns:
        Set of additional episode IDs that this file covers
    """
    import re
    
    # Create a mapping of episode numbers to episode IDs
    ep_num_to_id = {ep.get('episodeNumber'): ep.get('id') for ep in target_eps if ep.get('episodeNumber') and ep.get('id')}
    
    additional_episodes = set()
    
    # Pattern 1: S##E##-E## (most common multi-episode format)
    multi_ep_match = re.search(r'S(\d+)E(\d+)-E?(\d+)', title, re.IGNORECASE)
    if multi_ep_match:
        season_num = int(multi_ep_match.group(1))
        start_ep = int(multi_ep_match.group(2))
        end_ep = int(multi_ep_match.group(3))
        
        # Add all episodes in the range except the first one (which is already counted)
        for ep_num in range(start_ep + 1, end_ep + 1):
            if ep_num in ep_num_to_id:
                additional_episodes.add(ep_num_to_id[ep_num])
    
    # Pattern 2: S##E##E## (consecutive episodes without dash)
    consecutive_match = re.search(r'S(\d+)E(\d+)E(\d+)', title, re.IGNORECASE)
    if consecutive_match:
        season_num = int(consecutive_match.group(1))
        first_ep = int(consecutive_match.group(2))
        second_ep = int(consecutive_match.group(3))
        
        # Add the second episode (first is already counted)
        if second_ep in ep_num_to_id:
            additional_episodes.add(ep_num_to_id[second_ep])
    
    return additional_episodes
from .nzb_parser import NZBParser
from .core_analyzer import analyze_metadata
from .orchestrator_common import choose_best, load_config
from .sonarr_client import manual_search_episode, fetch_releases_for_episode

@dataclass
class CandidateDecision:
    episode_id: Optional[int]
    guid: str
    indexer: str
    title: str | None
    size: int | None
    decision: str | None
    risk_score: float | None
    probe_missing_ratio: float | None
    error: str | None = None


async def _analyze_release_parallel(rel, idx_cfg, parser, servers, sample_cap, semaphore):
    """Analyze a single release with concurrency control"""
    async with semaphore:  # Limit concurrent analyses
        try:
            nzb_id = extract_newznab_id(rel.guid)
            if not nzb_id:
                return {'guid': rel.guid, 'indexer': rel.indexer, 'error': 'No NZB ID', 'title': rel.title}
            
            print(f"   🔍 {datetime.now().strftime('%H:%M:%S')} Analyzing: {rel.title}")
            raw = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id, uid=idx_cfg.get('uid'))
            meta = parser.parse_bytes(raw)  # type: ignore
            report = await _analyze_release(meta, servers, sample_cap=sample_cap)
            decision = report.get('decision', 'UNKNOWN')
            risk_score = report.get('risk_score', 0.0)
            print(f"   ✅ {datetime.now().strftime('%H:%M:%S')} Result: {decision} (risk: {risk_score:.4f})")
            
            report.update({'guid': rel.guid, 'indexer': rel.indexer, 'title': rel.title, 'size': rel.size})
            return report
        except Exception as e:
            print(f"   ❌ {datetime.now().strftime('%H:%M:%S')} Error: {str(e)}")
            return {'guid': rel.guid, 'indexer': rel.indexer, 'error': str(e), 'title': rel.title}


async def _analyze_release_parallel_episode(rel, idx_cfg, parser, servers, sample_cap, semaphore, ep_id, decision_cache=None):
    """Analyze a single episode release with concurrency control, episode ID, and caching"""
    async with semaphore:  # Limit concurrent analyses
        try:
            nzb_id = extract_newznab_id(rel.guid)
            if not nzb_id:
                return {'guid': rel.guid, 'indexer': rel.indexer, 'error': 'No NZB ID', 'title': rel.title, 'episode_id': ep_id}
            
            # Create cache key based on NZB content
            cache_key = f"{rel.indexer}:{nzb_id}"
            
            # Check cache first
            if decision_cache:
                cached_result = decision_cache.get(cache_key)
                if cached_result:
                    print(f"   💾 {datetime.now().strftime('%H:%M:%S')} Cache hit: {rel.title} → {cached_result['decision']} (risk: {cached_result.get('risk', 0.0):.4f})")
                    return {
                        'guid': rel.guid, 
                        'indexer': rel.indexer, 
                        'title': rel.title, 
                        'episode_id': ep_id,
                        'decision': cached_result['decision'],
                        'risk_score': cached_result.get('risk', 0.0),
                        'probe_missing_ratio': cached_result.get('missing_ratio', 0.0),
                        'size': rel.size,
                        'cached': True
                    }
            
            print(f"   🔍 {datetime.now().strftime('%H:%M:%S')} Analyzing: {rel.title}")
            raw = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id, uid=idx_cfg.get('uid'))
            meta = parser.parse_bytes(raw)  # type: ignore
            report = await _analyze_release(meta, servers, sample_cap=sample_cap)
            decision = report.get('decision', 'UNKNOWN')
            risk_score = report.get('risk_score', 0.0)
            print(f"   ✅ {datetime.now().strftime('%H:%M:%S')} Result: {decision} (risk: {risk_score:.4f})")
            
            # Store in cache
            if decision_cache:
                decision_cache.put(cache_key, decision, report)
            
            report.update({'guid': rel.guid, 'indexer': rel.indexer, 'title': rel.title, 'size': rel.size, 'episode_id': ep_id})
            return report
        except Exception as e:
            print(f"   ❌ {datetime.now().strftime('%H:%M:%S')} Error: {str(e)}")
            return {'guid': rel.guid, 'indexer': rel.indexer, 'error': str(e), 'title': rel.title, 'episode_id': ep_id}


async def _analyze_release_parallel_pack(cand, idx_cfg, parser, servers, sample_cap, semaphore, decision_cache=None):
    """Analyze a single pack release with concurrency control and caching"""
    async with semaphore:  # Limit concurrent analyses
        try:
            guid = cand['guid']
            nzb_id = extract_newznab_id(guid)
            if not nzb_id:
                return {'guid': guid, 'indexer': cand['indexer'], 'error': 'No NZB ID', 'title': cand['title'], 'is_pack': True}
            
            # Create cache key for pack
            cache_key = f"{cand['indexer']}:{nzb_id}"
            
            # Check cache first
            if decision_cache:
                cached_result = decision_cache.get(cache_key)
                if cached_result:
                    print(f"   💾 {datetime.now().strftime('%H:%M:%S')} Cache hit pack: {cand['title']} → {cached_result['decision']} (risk: {cached_result.get('risk', 0.0):.4f})")
                    return {
                        'guid': guid,
                        'indexer': cand['indexer'],
                        'title': cand['title'],
                        'decision': cached_result['decision'],
                        'risk_score': cached_result.get('risk', 0.0),
                        'probe_missing_ratio': cached_result.get('missing_ratio', 0.0),
                        'size': cand.get('size'),
                        'is_pack': True,
                        'cached': True
                    }
            
            print(f"   🔍 {datetime.now().strftime('%H:%M:%S')} Analyzing pack: {cand['title']}")
            raw = fetch_nzb(idx_cfg['base_url'], idx_cfg['api_key'], nzb_id, uid=idx_cfg.get('uid'))
            meta = parser.parse_bytes(raw)  # type: ignore
            report = await _analyze_release(meta, servers, sample_cap=sample_cap)
            decision = report.get('decision', 'UNKNOWN')
            risk_score = report.get('risk_score', 0.0)
            print(f"   ✅ {datetime.now().strftime('%H:%M:%S')} Pack result: {decision} (risk: {risk_score:.4f})")
            
            # Store in cache
            if decision_cache:
                decision_cache.put(cache_key, decision, report)
            
            report.update({'guid': guid, 'indexer': cand['indexer'], 'title': cand['title'], 'size': cand.get('size'), 'is_pack': True})
            return report
        except Exception as e:
            print(f"   ❌ {datetime.now().strftime('%H:%M:%S')} Pack error: {str(e)}")
            return {'guid': cand['guid'], 'indexer': cand['indexer'], 'title': cand['title'], 'error': str(e), 'is_pack': True}


async def _analyze_release(meta: Any, servers: Any, sample_cap: int, retention_days: int = 4000):
    return await analyze_metadata(meta, servers=servers, retention_days=retention_days, dry_run=False, verbose=False, sample_cap=sample_cap)


from .pack_search import gather_pack_candidates
from .sonarr_client import manual_search_episode, fetch_releases_for_episode
from .types import SeasonPackCandidate

import re


def _extract_additional_episodes_from_title(title: str, target_eps: list) -> set:
    """
    Extract additional episode IDs that a multi-episode file covers.
    
    Args:
        title: Release title like "Adventure.Time.S09E02-E03.720p.HDTV.x264-W4F"
        target_eps: List of episode objects from Sonarr API with 'id' and 'episodeNumber' fields
    
    Returns:
        Set of additional episode IDs that this release covers
    """
    additional_episode_ids = set()
    
    # Create mapping from episode number to episode ID
    episode_num_to_id = {ep.get('episodeNumber'): ep.get('id') for ep in target_eps if ep.get('episodeNumber') and ep.get('id')}
    
    # Pattern 1: S##E##-E## (e.g., S09E02-E03)
    match = re.search(r'S(\d+)E(\d+)-E?(\d+)', title, re.IGNORECASE)
    if match:
        season_num = int(match.group(1))
        start_ep = int(match.group(2))
        end_ep = int(match.group(3))
        
        # Add all episodes in the range (excluding the first one which is already counted)
        for ep_num in range(start_ep + 1, end_ep + 1):
            if ep_num in episode_num_to_id:
                additional_episode_ids.add(episode_num_to_id[ep_num])
        return additional_episode_ids
    
    # Pattern 2: S##E##E## (e.g., S09E02E03)
    match = re.search(r'S(\d+)E(\d+)E(\d+)', title, re.IGNORECASE)
    if match:
        season_num = int(match.group(1))
        ep1 = int(match.group(2))
        ep2 = int(match.group(3))
        
        # Add the second episode (first is already counted)
        if ep2 in episode_num_to_id:
            additional_episode_ids.add(episode_num_to_id[ep2])
    
    return additional_episode_ids
    
    # Pattern 3: Multiple E## patterns (e.g., S09E02.E03.E04)
    episode_matches = re.findall(r'E(\d+)', title, re.IGNORECASE)
    if len(episode_matches) > 1:
        # Add all episodes after the first one
        for ep_str in episode_matches[1:]:
            ep_num = int(ep_str)
            if ep_num in episode_num_to_id:
                additional_episode_ids.add(episode_num_to_id[ep_num])
        return additional_episode_ids
    
    return additional_episode_ids


async def preflight_for_season(
    config_path: Path | str,
    servers_config_path: Path | str | None,
    sonarr_series_id: int,
    season_number: int,
    sonarr_url: str,
    sonarr_api_key: str,
    max_candidates: int = 15,
    sample_cap: int = 500,
    accept_threshold: float = 0.50,
    manual_search: bool = True,
    attempt_pack: bool = True,
    pack_category_ids: Sequence[int] | None = None,
    indexer_timeout: float = 5.0,
    specific_episodes: list[int] | None = None,  # List of episode numbers to limit analysis to
    download_callback: callable = None,  # Optional callback to start downloads immediately after each episode
) -> Dict[str, Any]:  # Using Dict for broad compatibility; internal structures use TypedDicts
    """
    DEPRECATED: This function is now a compatibility wrapper around the unified TV show preflight system.

    Run integrated preflight for all episodes in a season; optionally pivot to pack.

    Args:
        specific_episodes: Optional list of episode numbers (1-based) to limit analysis to.
                         If provided, only these episodes will be analyzed instead of all season episodes.

    Returns structured dict with keys:
      episodes: list of CandidateDecision dicts
      pack: list of CandidateDecision dicts (if attempted)
      best: winning candidate dict
      strategy: 'episodes' | 'episodes+pack' | 'pack_only'
      stats: counts & threshold evaluation
    """
    # Import the new unified system
    from .tv_show_preflight_selector import preflight_tv_show

    # Convert parameters to the new system's format
    # First, we need to get episode IDs for the season
    import urllib.request
    import urllib.parse
    import json

    # Fetch episodes for the season
    eps_url = f"{sonarr_url.rstrip('/')}/api/v3/episode"
    q = urllib.parse.urlencode({'seriesId': sonarr_series_id})
    req = urllib.request.Request(f"{eps_url}?{q}", headers={'X-Api-Key': sonarr_api_key})

    with urllib.request.urlopen(req, timeout=15.0) as resp:  # nosec
        all_episodes = json.loads(resp.read().decode('utf-8', 'replace'))

    # Filter to target season and specific episodes if provided
    target_episodes = [ep for ep in all_episodes if ep.get('seasonNumber') == season_number]
    if specific_episodes:
        target_episodes = [ep for ep in target_episodes if ep.get('episodeNumber') in specific_episodes]

    episode_ids = [ep.get('id') for ep in target_episodes if ep.get('id')]

    if not episode_ids:
        # Return empty result if no episodes found
        return {
            'episodes': [],
            'packs': [],
            'best': None,
            'strategy': 'none',
            'plan': {'episodes': [], 'pack': None},
            'stats': {
                'episodes_total': 0,
                'episodes_covered': 0,
                'episodes_accepted_files': 0,
                'best_files_per_episode': 0,
                'accept_fraction': 0.0,
                'threshold': accept_threshold,
                'attempt_pack': attempt_pack,
                'cache_hits': 0,
                'cache_hit_rate': 0.0
            }
        }

    # Call the new unified system
    result = await preflight_tv_show(
        series_id=sonarr_series_id,
        episode_ids=episode_ids,
        mode="standard",  # Use standard mode for backward compatibility
        callback=download_callback,
        config_path=Path(config_path),
        servers_config_path=Path(servers_config_path) if servers_config_path else None,
        sonarr_url=sonarr_url,
        sonarr_api_key=sonarr_api_key,
        max_candidates=max_candidates,
        sample_cap=sample_cap,
        accept_threshold=accept_threshold,
        manual_search=manual_search,
        attempt_pack=attempt_pack,
        pack_category_ids=pack_category_ids,
        indexer_timeout=indexer_timeout,
        season_number=season_number,
        specific_episodes=specific_episodes
    )

    # The new system returns the same format, so we can return it directly
    return result


__all__ = ['preflight_for_season']
