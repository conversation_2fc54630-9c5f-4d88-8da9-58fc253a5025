#!/usr/bin/env python3
"""
Test runner for the Unified Cache-Aware TV Show Preflight Analysis System.

This script runs all integration tests and provides comprehensive validation
of the unified system's functionality, compatibility, and performance.
"""

import asyncio
import sys
import unittest
import time
from pathlib import Path
from typing import List, Dict, Any

# Add the parent directory to the path so we can import the modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from preflight_analyzer.test_tv_show_preflight_selector import TestTvShowPreflightSelector
from preflight_analyzer.test_integrated_selector_compatibility import TestIntegratedSelectorCompatibility


class TestRunner:
    """Comprehensive test runner for the unified TV show preflight system."""
    
    def __init__(self):
        self.results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'errors': 0,
            'skipped': 0,
            'execution_time': 0.0
        }
        self.test_suites = [
            TestTvShowPreflightSelector,
            TestIntegratedSelectorCompatibility
        ]
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all test suites and return comprehensive results."""
        print("🧪 Starting Unified TV Show Preflight Analysis System Tests")
        print("=" * 70)
        
        start_time = time.time()
        
        for test_suite_class in self.test_suites:
            await self._run_test_suite(test_suite_class)
        
        end_time = time.time()
        self.results['execution_time'] = end_time - start_time
        
        self._print_summary()
        return self.results
    
    async def _run_test_suite(self, test_suite_class):
        """Run a single test suite."""
        suite_name = test_suite_class.__name__
        print(f"\n📋 Running {suite_name}")
        print("-" * 50)
        
        # Create test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(test_suite_class)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
        result = runner.run(suite)
        
        # Update results
        self.results['total_tests'] += result.testsRun
        self.results['passed'] += result.testsRun - len(result.failures) - len(result.errors) - len(result.skipped)
        self.results['failed'] += len(result.failures)
        self.results['errors'] += len(result.errors)
        self.results['skipped'] += len(result.skipped)
        
        # Print suite results
        if result.failures:
            print(f"\n❌ Failures in {suite_name}:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print(f"\n💥 Errors in {suite_name}:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    def _print_summary(self):
        """Print comprehensive test results summary."""
        print("\n" + "=" * 70)
        print("🎯 TEST EXECUTION SUMMARY")
        print("=" * 70)
        
        total = self.results['total_tests']
        passed = self.results['passed']
        failed = self.results['failed']
        errors = self.results['errors']
        skipped = self.results['skipped']
        execution_time = self.results['execution_time']
        
        print(f"Total Tests:     {total}")
        print(f"Passed:          {passed} ({(passed/total*100):.1f}%)" if total > 0 else "Passed:          0")
        print(f"Failed:          {failed} ({(failed/total*100):.1f}%)" if total > 0 else "Failed:          0")
        print(f"Errors:          {errors} ({(errors/total*100):.1f}%)" if total > 0 else "Errors:          0")
        print(f"Skipped:         {skipped} ({(skipped/total*100):.1f}%)" if total > 0 else "Skipped:         0")
        print(f"Execution Time:  {execution_time:.2f} seconds")
        
        # Overall status
        if failed == 0 and errors == 0:
            print(f"\n✅ ALL TESTS PASSED! The unified system is ready for production.")
            return True
        else:
            print(f"\n❌ TESTS FAILED! Please review and fix the issues above.")
            return False
    
    def run_specific_test(self, test_class_name: str, test_method_name: str = None):
        """Run a specific test class or method."""
        print(f"🎯 Running specific test: {test_class_name}")
        if test_method_name:
            print(f"   Method: {test_method_name}")
        
        # Find the test class
        test_class = None
        for suite_class in self.test_suites:
            if suite_class.__name__ == test_class_name:
                test_class = suite_class
                break
        
        if not test_class:
            print(f"❌ Test class '{test_class_name}' not found!")
            return False
        
        # Create and run the test
        if test_method_name:
            suite = unittest.TestSuite()
            suite.addTest(test_class(test_method_name))
        else:
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromTestCase(test_class)
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0


async def main():
    """Main entry point for the test runner."""
    runner = TestRunner()
    
    # Check for specific test arguments
    if len(sys.argv) > 1:
        test_class = sys.argv[1]
        test_method = sys.argv[2] if len(sys.argv) > 2 else None
        success = runner.run_specific_test(test_class, test_method)
        sys.exit(0 if success else 1)
    
    # Run all tests
    results = await runner.run_all_tests()
    
    # Exit with appropriate code
    if results['failed'] == 0 and results['errors'] == 0:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print(f"\n💥 Tests failed! {results['failed']} failures, {results['errors']} errors")
        sys.exit(1)


def run_validation_suite():
    """
    Quick validation suite for CI/CD or automated testing.
    
    This function provides a simplified interface for running
    the most critical tests to validate system functionality.
    """
    print("🚀 Running Unified TV Show Preflight Analysis Validation Suite")
    
    # Critical tests to run
    critical_tests = [
        ('TestTvShowPreflightSelector', 'test_standard_mode_analysis'),
        ('TestTvShowPreflightSelector', 'test_reliability_mode_analysis'),
        ('TestTvShowPreflightSelector', 'test_hybrid_mode_analysis'),
        ('TestIntegratedSelectorCompatibility', 'test_legacy_interface_compatibility'),
        ('TestIntegratedSelectorCompatibility', 'test_parameter_mapping')
    ]
    
    runner = TestRunner()
    all_passed = True
    
    for test_class, test_method in critical_tests:
        print(f"\n🔍 Validating: {test_class}.{test_method}")
        success = runner.run_specific_test(test_class, test_method)
        if not success:
            all_passed = False
            print(f"❌ Critical test failed: {test_class}.{test_method}")
    
    if all_passed:
        print("\n✅ All critical validation tests passed!")
        return True
    else:
        print("\n❌ Critical validation tests failed!")
        return False


if __name__ == '__main__':
    # Check for validation mode
    if len(sys.argv) > 1 and sys.argv[1] == '--validate':
        success = run_validation_suite()
        sys.exit(0 if success else 1)
    
    # Run full test suite
    asyncio.run(main())
