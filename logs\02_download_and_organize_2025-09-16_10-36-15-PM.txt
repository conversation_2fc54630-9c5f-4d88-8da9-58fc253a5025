=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-16 22:36:15
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-16_10-36-15-PM.txt
==================================================

[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-16_10-36-15-PM.txt
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-16 22:36:15
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] ==================================================
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,059 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO - Command-line mode: Processing both
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO - 🎬 Starting Radarr (Movies) monitoring...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO - 🔄 Checking for season progression opportunities...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,061 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,063 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,063 - pipeline_02 - INFO - 🔄 Initializing real-time telemetry for download monitoring...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,065 - pipeline_02 - INFO - No valid active jobs found in state file
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,065 - pipeline_02 - INFO - No movie candidates found in state file
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,065 - pipeline_02 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,065 - pipeline_02 - INFO - ✅ Real-time telemetry system initialized for download monitoring
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,065 - pipeline_02 - INFO -    🛡️ Intelligent fallback protection: ENABLED
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,065 - pipeline_02 - INFO - Discovering movies by scanning filesystem...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,070 - pipeline_02 - INFO - Found 2 movies across 14 stages
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,070 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,070 - pipeline_02 - INFO -      Radarr API endpoint: http://localhost:7878
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,070 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,082 - pipeline_02 - INFO - Retrieved 1 movies from Radarr
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,083 - pipeline_02 - INFO - No active downloads in Radarr queue
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,085 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,085 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO - No movies currently in download states
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO - 🛡️  LONG PATH PRE-PROCESSING: Checking for Windows path length issues...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO -      Found 1 items to check for long paths
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO -      Checking folder: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD (104 chars)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO -           MKV file: futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv (176 chars)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,088 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD\futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv
[2025-09-16 22:36:15] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD (140 chars)
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00]     DEBUG: MKV file: futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD\futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv (212 chars)
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,089 - pipeline_02 - INFO -      Long path handling completed - safe to proceed with detection
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,089 - pipeline_02 - INFO - 🔍 Checking for Windows 8.3 short name corruption...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      No completed movies found in filesystem
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      Content type filter: movie
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      ENHANCED DETECTION: Scanning for both movies and TV shows...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      FILESYSTEM SCAN: Found 0 movies and 0 TV shows
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      TOTAL CONTENT: 0 items ready for processing
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,090 - pipeline_02 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,094 - pipeline_02 - INFO -      Radarr API: Retrieved 1 movies for status sync
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,096 - pipeline_02 - INFO - Pipeline state refreshed - found 2 movies
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,096 - pipeline_02 - INFO - ✅ Real-time telemetry system cleaned up
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,096 - pipeline_02 - INFO - ===== Finished Modern Radarr Download Monitoring =====
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,096 - pipeline_02 - INFO -     No new completed downloads found this run
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,096 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,097 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,097 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,097 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,097 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,100 - pipeline_02 - INFO - Found 2 content items across 14 stages
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,100 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,100 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,100 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,103 - pipeline_02 - INFO - Retrieved 2 TV series from Sonarr
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,105 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 1
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,105 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,105 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,106 - pipeline_02 - INFO - Selected main video file: futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv (0.85 GB)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,106 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD (1 video files, ~0.85 GB)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,106 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 1 completed TV folders ready for organization
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,106 - pipeline_02 - INFO -      Processing 1 completed TV folders
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,134 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD, processing...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD', 'confidence': 0.95}
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,135 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD'
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,138 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,138 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,138 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,138 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,138 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,173 - pipeline_02 - INFO - Detected resolution 1920x1080 for futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,173 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,173 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,501 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,502 - pipeline_02 - INFO - Created directory: workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,503 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD\futurama.s01e01.zeit.und.raum.3000.german.dl.fs.1080p.web.h264-cnhd.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.mkv'
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,503 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,503 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD (ignore_errors=True)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,507 - pipeline_02 - INFO - 📝 Updated .organized - S01E01 with metadata
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,507 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E01
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,509 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,509 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -    Checking 1 queue items for pending episodes...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -       - Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD (Status: downloading)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (Movies + TV Shows)
[2025-09-16 22:36:15] [STDERR] [+0:00:00] 2025-09-16 22:36:15,511 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 🕐 Ended at: 2025-09-16 22:36:15
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] ⏱️ Total duration: 0:00:00.455878
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-16_10-36-15-PM.txt
[2025-09-16 22:36:15] [STDOUT] [+0:00:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-16 22:36:15
Duration: 0:00:00.455878
==================================================
